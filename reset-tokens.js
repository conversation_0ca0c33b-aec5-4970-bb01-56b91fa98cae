#!/usr/bin/env node

/**
 * Reset Token Status Script
 * Resets all tokens to valid state for development/testing
 */

const fs = require('fs');
const path = require('path');

const TOKENS_FILE = './tokens.json';

function resetTokens() {
    try {
        console.log('🔄 Resetting token status...');
        
        if (!fs.existsSync(TOKENS_FILE)) {
            console.error(`❌ Token file not found: ${TOKENS_FILE}`);
            process.exit(1);
        }

        // Read current tokens
        const data = JSON.parse(fs.readFileSync(TOKENS_FILE, 'utf8'));
        
        if (!data.tokens || !Array.isArray(data.tokens)) {
            console.error('❌ Invalid token file format');
            process.exit(1);
        }

        console.log(`📥 Found ${data.tokens.length} tokens`);

        // Reset each token
        let resetCount = 0;
        data.tokens.forEach(token => {
            // Force reset all tokens regardless of current state
            token.isValid = true;
            token.invalidAttempts = 0;
            token.bannedUntil = null;
            token.lastValidated = null; // Force re-validation
            token.invalidReason = null;
            token.assignedTo = null;
            resetCount++;
        });

        // Write back to file
        fs.writeFileSync(TOKENS_FILE, JSON.stringify(data, null, 2));

        // Verify the write was successful
        const verifyData = JSON.parse(fs.readFileSync(TOKENS_FILE, 'utf8'));
        const validTokens = verifyData.tokens.filter(t => t.isValid === true).length;

        console.log(`✅ Reset ${resetCount} tokens`);
        console.log(`🔍 Verification: ${validTokens} tokens are now valid`);
        console.log('🎉 Token reset completed successfully!');
        
    } catch (error) {
        console.error('❌ Failed to reset tokens:', error.message);
        process.exit(1);
    }
}

// Run the reset
resetTokens();
