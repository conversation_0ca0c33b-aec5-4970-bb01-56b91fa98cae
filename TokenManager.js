const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * 分布式Token管理器 (最终锁修正版)
 * 1. 使用独立的、无锁的心跳文件，避免不必要的锁竞争。
 * 2. 引入统一的原子更新函数，将所有文件修改操作集中管理，最大限度缩短锁持有时间。
 * 3. 实现指数退避的锁重试策略，智能地处理高并发冲突。
 * 4. 修正了锁获取失败时无法正确执行解锁逻辑的缺陷。
 */
class TokenManager {
    constructor(options = {}) {
        this.tokensFile = options.tokensFile || './tokens.json';
        this.instanceId = options.instanceId || `manager_${process.pid}_${crypto.randomBytes(4).toString('hex')}`;
        
        // --- 锁和心跳配置 ---
        this.lockFile = `${this.tokensFile}.lock`;
        this.lockTimeout = options.lockTimeout || 30000; // 30秒锁超时
        this.heartbeatDir = './heartbeats'; // 为心跳信息创建独立目录
        this.heartbeatInterval = 10000; // 10秒心跳间隔

        this.currentTokenId = null;
        this.heartbeatTimer = null;

        // 确保心跳目录存在
        if (!fs.existsSync(this.heartbeatDir)) {
            fs.mkdirSync(this.heartbeatDir, { recursive: true });
        }
    }

    /**
     * 获取文件锁，用于修改 tokens.json (带指数退避策略)
     */
    async acquireLock() {
        const startTime = Date.now();
        let waitTime = 50; // 初始等待时间 50ms
        const maxWaitTime = 500; // 最大等待时间 500ms

        while (Date.now() - startTime < this.lockTimeout) {
            try {
                if (fs.existsSync(this.lockFile)) {
                    const lockData = JSON.parse(fs.readFileSync(this.lockFile, 'utf8'));
                    if (Date.now() - lockData.timestamp > this.lockTimeout) {
                        console.warn(`[${this.instanceId}] 发现并清理超时Token锁: ${lockData.instanceId}`);
                        fs.unlinkSync(this.lockFile);
                    }
                }
                const lockData = { instanceId: this.instanceId, timestamp: Date.now() };
                fs.writeFileSync(this.lockFile, JSON.stringify(lockData), { flag: 'wx' });
                return true;
            } catch (error) {
                if (error.code !== 'EEXIST') console.error(`[${this.instanceId}] 获取Token锁出错: ${error.message}`);
                
                // 指数退避等待
                await new Promise(resolve => setTimeout(resolve, waitTime));
                waitTime = Math.min(waitTime * 2, maxWaitTime); // 等待时间加倍，但不超过最大值
            }
        }
        console.error(`[${this.instanceId}] 获取Token锁超时!`);
        return false;
    }

    /**
     * 释放文件锁
     */
    releaseLock() {
        try {
            if (fs.existsSync(this.lockFile)) {
                const lockData = JSON.parse(fs.readFileSync(this.lockFile, 'utf8'));
                if (lockData.instanceId === this.instanceId) {
                    fs.unlinkSync(this.lockFile);
                }
            }
        } catch (error) {
             console.warn(`[${this.instanceId}] 释放Token锁出错: ${error.message}`);
        }
    }
    
    /**
     * [私有] 加载并标准化tokens，不处理锁
     */
    _loadTokensInternal() {
        if (!fs.existsSync(this.tokensFile)) throw new Error(`Token文件不存在: ${this.tokensFile}`);
        const data = fs.readFileSync(this.tokensFile, 'utf8');
        const config = JSON.parse(data);
        return (config.tokens || []).map((token, index) => {
            const base = {
                isValid: true, // Default to true at startup - assume tokens are valid
                lastValidated: null,
                bannedUntil: null,
                assignedTo: null,
                invalidReason: null,
            };
            if(typeof token === 'string') {
                return {...base, id: index + 1, token: token};
            }
            // Override with token data but ensure isValid defaults to true if not explicitly false
            const tokenData = {...base, ...token, id: token.id || index + 1};
            if (tokenData.isValid !== false) {
                tokenData.isValid = true;
            }
            return tokenData;
        });
    }

    /**
     * [私有] 保存tokens状态，不处理锁
     */
    _saveTokensInternal(tokens) {
        const tokenConfig = {
            lastUpdated: new Date().toISOString(),
            updatedBy: this.instanceId,
            tokens: tokens,
        };
        const tempFile = `${this.tokensFile}.${crypto.randomBytes(4).toString('hex')}.tmp`;
        fs.writeFileSync(tempFile, JSON.stringify(tokenConfig, null, 2), 'utf8');
        fs.renameSync(tempFile, this.tokensFile);
    }
    
    /**
     * [核心] 对 tokens.json 执行原子更新操作
     * @param {function(Array<object>): Array<object> | null} updateFunction 一个函数，接收当前token列表，返回修改后的列表或null
     * @returns {Promise<boolean>} 操作是否成功
     */
    async atomicUpdate(updateFunction) {
        let lockAcquired = false;
        try {
            lockAcquired = await this.acquireLock();
            if (!lockAcquired) {
                // 如果获取锁失败（超时），直接抛出错误
                throw new Error("无法获取Token锁以执行原子更新");
            }
            
            const currentTokens = this._loadTokensInternal();
            const modifiedTokens = updateFunction(currentTokens);

            // 如果更新函数返回了一个有效的数组，则保存它
            if (Array.isArray(modifiedTokens)) {
                this._saveTokensInternal(modifiedTokens);
                return true;
            }
            // 如果返回 null 或其他，则认为不需要更新
            return false;
        } finally {
            // 关键修正：无论try块中发生什么，都确保释放锁
            if(lockAcquired) {
                this.releaseLock();
            }
        }
    }

    /**
     * 为实例分配一个可用的token
     */
    async assignTokenToInstance() {
        console.log(`🔍 [${this.instanceId}] 正在分配Token...`);
        let assignedToken = null;

        await this.atomicUpdate(tokens => {
            const now = Date.now();
            let reclaimedCount = 0;
            
            // 先回收僵尸token（没有心跳的已分配token）
            console.log(`🧹 [${this.instanceId}] 开始自动清理僵尸Token...`);
            tokens.forEach(token => {
                if (token.assignedTo) {
                    const heartbeatFile = path.join(this.heartbeatDir, `${token.assignedTo}.heartbeat`);
                    let shouldReclaim = false;
                    let reason = '';
                    
                    if (!fs.existsSync(heartbeatFile)) {
                        shouldReclaim = true;
                        reason = '心跳文件不存在';
                    } else {
                        try {
                            const heartbeatData = JSON.parse(fs.readFileSync(heartbeatFile, 'utf8'));
                            const heartbeatAge = now - new Date(heartbeatData.timestamp).getTime();
                            if (heartbeatAge > this.heartbeatInterval * 3) {
                                shouldReclaim = true;
                                reason = `心跳超时(${Math.round(heartbeatAge/1000)}秒)`;
                            }
                        } catch (error) {
                            shouldReclaim = true;
                            reason = '心跳文件损坏';
                        }
                    }
                    
                    if (shouldReclaim) {
                        console.log(`🔄 [${this.instanceId}] 回收僵尸Token[${token.id}]，原分配给: ${token.assignedTo}，原因: ${reason}`);
                        token.assignedTo = null;
                        delete token.assignedAt;
                        reclaimedCount++;
                    }
                }
            });
            
            if (reclaimedCount > 0) {
                console.log(`♻️ [${this.instanceId}] 自动回收完成，共释放了 ${reclaimedCount} 个僵尸Token`);
            } else {
                console.log(`✅ [${this.instanceId}] 自动清理完成，没有发现僵尸Token`);
            }
            
            // 过滤可用的token
            const availableTokens = tokens.filter(token => {
                if (token.isValid === false) return false;
                if (token.bannedUntil && now < new Date(token.bannedUntil).getTime()) return false;
                if (token.assignedTo) return false; // 已被分配且实例存活
                return true;
            });

            if (availableTokens.length === 0) {
                console.warn(`⚠️ [${this.instanceId}] 没有可用的Token。总数: ${tokens.length}, 无效: ${tokens.filter(t => t.isValid === false).length}, 被封禁: ${tokens.filter(t => t.bannedUntil && now < new Date(t.bannedUntil).getTime()).length}, 已分配: ${tokens.filter(t => t.assignedTo).length}`);
                return null; // 没有可用token，不修改
            }

            // 优先选择从未被分配过的token
            availableTokens.sort((a, b) => {
                const aUsed = a.assignedTo !== null || a.lastValidated !== null;
                const bUsed = b.assignedTo !== null || b.lastValidated !== null;
                return aUsed - bUsed;
            });
            
            const selectedToken = availableTokens[0];
            selectedToken.assignedTo = this.instanceId;
            selectedToken.assignedAt = new Date().toISOString();
            assignedToken = selectedToken; // 在闭包外捕获选中的token

            return tokens; // 返回修改后的数组以供保存
        });

        if (assignedToken) {
            this.currentTokenId = assignedToken.id;
            this.startHeartbeat();
            console.log(`✅ [${this.instanceId}] 成功分配 Token[${assignedToken.id}]`);
            return assignedToken;
        } else {
            throw new Error('没有可用的Token，请检查tokens.json');
        }
    }

    /**
     * 释放当前实例占用的token
     */
    async releaseToken(tokenId) {
        this.stopHeartbeat();
        await this.atomicUpdate(tokens => {
            const token = tokens.find(t => t.id === tokenId && t.assignedTo === this.instanceId);
            if (token) {
                console.log(`[${this.instanceId}] 已释放 Token[${tokenId}]`);
                token.assignedTo = null;
                return tokens;
            }
            return null; // Token不属于此实例或未找到，不修改
        });
    }
    
    /**
     * 标记token为永久无效
     */
    async markTokenInvalid(tokenId, reason = 'unknown') {
        this.stopHeartbeat();
        await this.atomicUpdate(tokens => {
            const token = tokens.find(t => t.id === tokenId);
            if (token) {
                console.log(`🚫 [${this.instanceId}] 已将 Token[${tokenId}] 标记为永久无效。原因: ${reason}`);
                token.isValid = false;
                token.invalidReason = reason;
                token.assignedTo = null; // 同时释放
                return tokens;
            }
            return null;
        });
    }

    /**
     * 启动心跳，只写自己的心跳文件，不锁定公共资源
     */
    startHeartbeat() {
        if (this.heartbeatTimer) clearInterval(this.heartbeatTimer);
        
        const heartbeatFile = path.join(this.heartbeatDir, `${this.instanceId}.heartbeat`);
        
        const updateHeartbeat = () => {
             try {
                fs.writeFileSync(heartbeatFile, JSON.stringify({ timestamp: new Date().toISOString() }));
             } catch (e) {
                console.warn(`[${this.instanceId}] 写入心跳失败: ${e.message}`);
             }
        };

        this.heartbeatTimer = setInterval(updateHeartbeat, this.heartbeatInterval);
        updateHeartbeat(); // 立即执行一次
    }
    
    /**
     * 停止心跳并清理心跳文件
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
        try {
            const heartbeatFile = path.join(this.heartbeatDir, `${this.instanceId}.heartbeat`);
            if (fs.existsSync(heartbeatFile)) {
                fs.unlinkSync(heartbeatFile);
            }
        } catch (e) {
            console.warn(`[${this.instanceId}] 清理心跳文件失败: ${e.message}`);
        }
    }

    /**
     * 强制清理所有僵尸token（工具方法）
     */
    async cleanupZombieTokens() {
        console.log(`🧹 [${this.instanceId}] 开始清理僵尸Token...`);
        
        let cleanedCount = 0;
        await this.atomicUpdate(tokens => {
            const now = Date.now();
            
            tokens.forEach(token => {
                if (token.assignedTo) {
                    const heartbeatFile = path.join(this.heartbeatDir, `${token.assignedTo}.heartbeat`);
                    let shouldClean = false;
                    
                    if (!fs.existsSync(heartbeatFile)) {
                        shouldClean = true;
                    } else {
                        try {
                            const heartbeatData = JSON.parse(fs.readFileSync(heartbeatFile, 'utf8'));
                            const heartbeatAge = now - new Date(heartbeatData.timestamp).getTime();
                            if (heartbeatAge > this.heartbeatInterval * 2) { // 更严格的清理条件
                                shouldClean = true;
                            }
                        } catch (error) {
                            shouldClean = true;
                        }
                    }
                    
                    if (shouldClean) {
                        console.log(`🗑️ [${this.instanceId}] 清理僵尸Token[${token.id}]，原分配给: ${token.assignedTo}`);
                        token.assignedTo = null;
                        delete token.assignedAt;
                        cleanedCount++;
                    }
                }
            });
            
            return cleanedCount > 0 ? tokens : null;
        });
        
        console.log(`✅ [${this.instanceId}] 僵尸Token清理完成，共清理 ${cleanedCount} 个`);
        return cleanedCount;
    }

    /**
     * 获取Token统计信息
     */
    getTokenStats() {
        try {
            const tokens = this._loadTokensInternal();
            const now = Date.now();
            
            return {
                total: tokens.length,
                available: tokens.filter(t => 
                    t.isValid !== false && 
                    (!t.bannedUntil || now >= new Date(t.bannedUntil).getTime()) &&
                    !t.assignedTo
                ).length,
                assigned: tokens.filter(t => t.assignedTo).length,
                invalid: tokens.filter(t => t.isValid === false).length,
                banned: tokens.filter(t => t.bannedUntil && now < new Date(t.bannedUntil).getTime()).length
            };
        } catch (error) {
            console.error(`❌ [${this.instanceId}] 获取Token统计失败: ${error.message}`);
            return { total: 0, available: 0, assigned: 0, invalid: 0, banned: 0 };
        }
    }

    /**
     * 快速同步释放当前Token（用于紧急情况）
     */
    forceReleaseCurrentToken() {
        if (!this.currentTokenId) return false;
        
        try {
            const tokenId = this.currentTokenId;
            console.log(`⚡ [${this.instanceId}] 快速释放Token[${tokenId}]...`);
            
            // 同步方式直接修改tokens.json
            if (fs.existsSync(this.tokensFile)) {
                const tokensData = fs.readFileSync(this.tokensFile, 'utf8');
                const tokenConfig = JSON.parse(tokensData);
                
                if (tokenConfig.tokens && Array.isArray(tokenConfig.tokens)) {
                    const token = tokenConfig.tokens.find(t => t.id === tokenId && t.assignedTo === this.instanceId);
                    if (token) {
                        token.assignedTo = null;
                        delete token.assignedAt;
                        
                        // 同步写入
                        fs.writeFileSync(this.tokensFile, JSON.stringify(tokenConfig, null, 2), 'utf8');
                        console.log(`✅ [${this.instanceId}] Token[${tokenId}]已快速释放`);
                        
                        this.currentTokenId = null;
                        this.stopHeartbeat();
                        return true;
                    }
                }
            }
        } catch (error) {
            console.error(`❌ [${this.instanceId}] 快速释放Token失败: ${error.message}`);
        }
        return false;
    }

    /**
     * 销毁管理器实例
     */
    async destroy() {
        console.log(`🔄 [${this.instanceId}] 正在销毁TokenManager...`);
        
        // 主动释放当前占用的token
        if (this.currentTokenId) {
            try {
                await this.releaseToken(this.currentTokenId);
                console.log(`✅ [${this.instanceId}] 已释放Token[${this.currentTokenId}]`);
            } catch (error) {
                console.warn(`⚠️ [${this.instanceId}] 释放Token[${this.currentTokenId}]失败，尝试强制释放: ${error.message}`);
                // 如果异步释放失败，使用同步方式
                this.forceReleaseCurrentToken();
            }
            this.currentTokenId = null;
        }
        
        // 停止心跳
        this.stopHeartbeat();
        
        console.log(`✅ [${this.instanceId}] TokenManager已销毁`);
    }
}

module.exports = TokenManager;
