const fs = require('fs');
const path = require('path');

/**
 * 将数组均分成n份
 * @param {Array} array - 要分割的数组
 * @param {number} n - 分割份数
 * @returns {Array} 分割后的数组组合
 */
function splitArrayIntoChunks(array, n) {
    if (n <= 0) {
        throw new Error('分割份数必须大于0');
    }
    
    if (n >= array.length) {
        // 如果份数大于等于数组长度，每个元素单独一份
        return array.map(item => [item]);
    }
    
    const totalLength = array.length;
    const baseSize = Math.floor(totalLength / n); // 基础大小
    const remainder = totalLength % n; // 余数
    
    const chunks = [];
    let currentIndex = 0;
    
    // 前 remainder 个块多分配一个元素，后面的块使用基础大小
    for (let i = 0; i < n; i++) {
        const chunkSize = i < remainder ? baseSize + 1 : baseSize;
        chunks.push(array.slice(currentIndex, currentIndex + chunkSize));
        currentIndex += chunkSize;
    }
    
    return chunks;
}

/**
 * 主函数
 * @param {number} n - 分割份数
 */
function splitTokens(n) {
    try {
        // 读取原始tokens文件
        const tokensPath = path.join(__dirname, 'tokens.json');
        if (!fs.existsSync(tokensPath)) {
            throw new Error('tokens.json文件不存在');
        }
        
        const data = JSON.parse(fs.readFileSync(tokensPath, 'utf8'));
        
        if (!data.tokens || !Array.isArray(data.tokens)) {
            throw new Error('tokens.json文件格式不正确，缺少tokens数组');
        }
        
        console.log(`原始tokens数量: ${data.tokens.length}`);
        console.log(`准备分割成 ${n} 份`);
        
        // 分割tokens数组
        const tokenChunks = splitArrayIntoChunks(data.tokens, n);
        
        console.log(`实际分割成 ${tokenChunks.length} 份`);
        
        // 保存分割后的文件
        tokenChunks.forEach((chunk, index) => {
            const newData = {
                ...data,
                tokens: chunk,
                totalProcessed: chunk.length,
                successCount: chunk.filter(token => token.isValid).length,
                splitInfo: {
                    partIndex: index + 1,
                    totalParts: tokenChunks.length,
                    originalTotal: data.tokens.length,
                    splitAt: new Date().toISOString()
                }
            };
            
            const fileName = `tokens-${index + 1}.json`;
            const filePath = path.join(__dirname, fileName);
            
            fs.writeFileSync(filePath, JSON.stringify(newData, null, 2), 'utf8');
            
            console.log(`已保存: ${fileName} (包含 ${chunk.length} 个tokens)`);
        });
        
        // 创建汇总信息文件
        const summaryData = {
            splitInfo: {
                totalParts: tokenChunks.length,
                originalTotal: data.tokens.length,
                splitAt: new Date().toISOString(),
                parts: tokenChunks.map((chunk, index) => ({
                    partIndex: index + 1,
                    fileName: `tokens-part-${index + 1}.json`,
                    tokenCount: chunk.length,
                    validTokens: chunk.filter(token => token.isValid).length
                }))
            }
        };
        
        fs.writeFileSync(
            path.join(__dirname, 'tokens-split-summary.json'),
            JSON.stringify(summaryData, null, 2),
            'utf8'
        );
        
        console.log('\n分割完成！');
        console.log('已生成文件:');
        tokenChunks.forEach((chunk, index) => {
            console.log(`  - tokens-part-${index + 1}.json (${chunk.length} tokens)`);
        });
        console.log('  - tokens-split-summary.json (汇总信息)');
        
    } catch (error) {
        console.error('错误:', error.message);
        process.exit(1);
    }
}

// 命令行参数处理
const args = process.argv.slice(2);
if (args.length === 0) {
    console.log('使用方法: node split-tokens.js <分割份数>');
    console.log('例如: node split-tokens.js 3');
    process.exit(1);
}

const n = parseInt(args[0]);
if (isNaN(n) || n <= 0) {
    console.error('分割份数必须是一个正整数');
    process.exit(1);
}

splitTokens(n); 