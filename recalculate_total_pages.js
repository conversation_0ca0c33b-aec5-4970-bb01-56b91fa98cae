const fs = require('fs');
const path = require('path');

// 配置
const MAX_PAGES_LIMIT = 1000; // 每个参数组合的最大页数限制

// 查找最新的 total_pages_summary 文件
const findLatestSummaryFile = () => {
    try {
        const files = fs.readdirSync('.');
        const summaryFiles = files.filter(file => 
            file.startsWith('total_pages_summary_') && file.endsWith('.json')
        );
        
        if (summaryFiles.length === 0) {
            throw new Error('未找到 total_pages_summary 文件');
        }
        
        // 按文件名排序，获取最新的
        summaryFiles.sort((a, b) => b.localeCompare(a));
        return summaryFiles[0];
    } catch (error) {
        console.error('❌ 查找summary文件失败:', error.message);
        return null;
    }
};

// 重新计算统计数据
const recalculateStatistics = (originalResults) => {
    console.log('🔄 重新计算统计数据...');
    
    // 处理每个结果，限制最大页数
    const processedResults = originalResults.map(result => {
        const originalTotalPage = result.totalPage;
        const limitedTotalPage = originalTotalPage !== null ? Math.min(originalTotalPage, MAX_PAGES_LIMIT) : null;
        
        return {
            ...result,
            originalTotalPage: originalTotalPage, // 保留原始值
            totalPage: limitedTotalPage, // 限制后的值
            limitApplied: originalTotalPage > MAX_PAGES_LIMIT // 是否应用了限制
        };
    });
    
    // 重新计算各维度统计
    const statistics = {
        totalPagesByPhase: {},
        totalPagesBySubject: {},
        totalPagesByCombination: {},
        limitedVsOriginal: {
            totalOriginalPages: 0,
            totalLimitedPages: 0,
            affectedCombinations: 0,
            limitAppliedCount: 0
        }
    };
    
    processedResults.forEach(result => {
        if (result.totalPage !== null) {
            const originalPage = result.originalTotalPage;
            const limitedPage = result.totalPage;
            
            // 统计限制影响
            statistics.limitedVsOriginal.totalOriginalPages += originalPage;
            statistics.limitedVsOriginal.totalLimitedPages += limitedPage;
            
            if (result.limitApplied) {
                statistics.limitedVsOriginal.limitAppliedCount++;
            }
            
            // 按学段统计
            const phase = result.studyPhaseName;
            if (!statistics.totalPagesByPhase[phase]) {
                statistics.totalPagesByPhase[phase] = {
                    original: 0,
                    limited: 0,
                    combinations: 0,
                    limitedCombinations: 0
                };
            }
            statistics.totalPagesByPhase[phase].original += originalPage;
            statistics.totalPagesByPhase[phase].limited += limitedPage;
            statistics.totalPagesByPhase[phase].combinations++;
            if (result.limitApplied) {
                statistics.totalPagesByPhase[phase].limitedCombinations++;
            }
            
            // 按学科统计
            const subject = `${result.studyPhaseName}-${result.subjectName}`;
            if (!statistics.totalPagesBySubject[subject]) {
                statistics.totalPagesBySubject[subject] = {
                    original: 0,
                    limited: 0,
                    combinations: 0,
                    limitedCombinations: 0
                };
            }
            statistics.totalPagesBySubject[subject].original += originalPage;
            statistics.totalPagesBySubject[subject].limited += limitedPage;
            statistics.totalPagesBySubject[subject].combinations++;
            if (result.limitApplied) {
                statistics.totalPagesBySubject[subject].limitedCombinations++;
            }
            
            // 按完整组合统计
            const combination = `${result.studyPhaseName}/${result.subjectName}/${result.textbookVersionName}/${result.ceciName}`;
            if (!statistics.totalPagesByCombination[combination]) {
                statistics.totalPagesByCombination[combination] = {
                    original: 0,
                    limited: 0,
                    catalogCount: 0,
                    limitedCatalogCount: 0
                };
            }
            statistics.totalPagesByCombination[combination].original += originalPage;
            statistics.totalPagesByCombination[combination].limited += limitedPage;
            statistics.totalPagesByCombination[combination].catalogCount++;
            if (result.limitApplied) {
                statistics.totalPagesByCombination[combination].limitedCatalogCount++;
            }
        }
    });
    
    // 计算受影响的组合数
    statistics.limitedVsOriginal.affectedCombinations = Object.keys(statistics.totalPagesByCombination).length;
    
    return { processedResults, statistics };
};

// 生成详细报告
const generateDetailedReport = (originalData, processedData, statistics) => {
    const report = {
        summary: {
            maxPagesLimit: MAX_PAGES_LIMIT,
            processedAt: new Date().toISOString(),
            originalFile: originalData.inputFile,
            totalCombinations: processedData.length,
            originalTotalPages: statistics.limitedVsOriginal.totalOriginalPages,
            limitedTotalPages: statistics.limitedVsOriginal.totalLimitedPages,
            savedPages: statistics.limitedVsOriginal.totalOriginalPages - statistics.limitedVsOriginal.totalLimitedPages,
            limitAppliedCount: statistics.limitedVsOriginal.limitAppliedCount,
            limitAppliedPercentage: ((statistics.limitedVsOriginal.limitAppliedCount / processedData.length) * 100).toFixed(2) + '%'
        },
        
        impactAnalysis: {
            pageReduction: {
                absolute: statistics.limitedVsOriginal.totalOriginalPages - statistics.limitedVsOriginal.totalLimitedPages,
                percentage: (((statistics.limitedVsOriginal.totalOriginalPages - statistics.limitedVsOriginal.totalLimitedPages) / statistics.limitedVsOriginal.totalOriginalPages) * 100).toFixed(2) + '%'
            }
        },
        
        statistics: statistics,
        
        topAffectedCombinations: [],
        
        processedResults: processedData
    };
    
    // 找出受影响最大的组合
    const affectedCombinations = processedData
        .filter(result => result.limitApplied)
        .map(result => ({
            combination: `${result.studyPhaseName}/${result.subjectName}/${result.textbookVersionName}/${result.ceciName}/${result.catalogName}`,
            originalPages: result.originalTotalPage,
            limitedPages: result.totalPage,
            savedPages: result.originalTotalPage - result.totalPage
        }))
        .sort((a, b) => b.savedPages - a.savedPages)
        .slice(0, 20); // 取前20个受影响最大的
    
    report.topAffectedCombinations = affectedCombinations;
    
    return report;
};

// 主函数
const main = async () => {
    console.log('🚀 开始重新计算总页数统计...\n');
    
    // 查找最新的summary文件
    const summaryFile = findLatestSummaryFile();
    if (!summaryFile) {
        console.error('❌ 未找到有效的summary文件');
        return;
    }
    
    console.log(`📂 使用文件: ${summaryFile}`);
    
    try {
        // 读取原始数据
        const originalData = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
        originalData.inputFile = summaryFile;
        
        if (!originalData.results || !Array.isArray(originalData.results)) {
            throw new Error('原始文件格式错误：缺少results数组');
        }
        
        console.log(`📊 原始数据: ${originalData.results.length} 个参数组合`);
        console.log(`🔢 页数限制: 每个组合最大 ${MAX_PAGES_LIMIT} 页`);
        
        // 重新计算统计
        const { processedResults, statistics } = recalculateStatistics(originalData.results);
        
        // 生成详细报告
        const report = generateDetailedReport(originalData, processedResults, statistics);
        
        // 保存结果
        const outputFilename = `recalculated_total_pages_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.json`;
        fs.writeFileSync(outputFilename, JSON.stringify(report, null, 2));
        
        // 显示统计结果
        console.log('\n📊 重新计算完成！');
        console.log('=' * 50);
        console.log(`📈 原始总页数: ${statistics.limitedVsOriginal.totalOriginalPages.toLocaleString()}`);
        console.log(`📉 限制后总页数: ${statistics.limitedVsOriginal.totalLimitedPages.toLocaleString()}`);
        console.log(`💾 节省页数: ${(statistics.limitedVsOriginal.totalOriginalPages - statistics.limitedVsOriginal.totalLimitedPages).toLocaleString()}`);
        console.log(`🎯 受影响组合: ${statistics.limitedVsOriginal.limitAppliedCount} / ${processedResults.length} (${report.summary.limitAppliedPercentage})`);
        console.log(`📐 页数缩减: ${report.impactAnalysis.pageReduction.percentage}`);
        console.log('=' * 50);
        
        // 显示学段统计
        console.log('\n📚 按学段统计:');
        Object.entries(statistics.totalPagesByPhase).forEach(([phase, data]) => {
            console.log(`  ${phase}:`);
            console.log(`    原始页数: ${data.original.toLocaleString()}, 限制后: ${data.limited.toLocaleString()}`);
            console.log(`    组合数: ${data.combinations}, 受影响: ${data.limitedCombinations}`);
        });
        
        // 显示受影响最大的组合
        if (report.topAffectedCombinations.length > 0) {
            console.log('\n🔝 受影响最大的组合 (前10个):');
            report.topAffectedCombinations.slice(0, 10).forEach((item, index) => {
                console.log(`  ${index + 1}. ${item.combination}`);
                console.log(`     ${item.originalPages} → ${item.limitedPages} (节省 ${item.savedPages} 页)`);
            });
        }
        
        console.log(`\n💾 详细报告已保存到: ${outputFilename}`);
        
    } catch (error) {
        console.error('❌ 处理失败:', error.message);
        return;
    }
};

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
});

process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    process.exit(1);
});

// 运行主函数
main().catch(error => {
    console.error('主函数执行失败:', error);
    process.exit(1);
});