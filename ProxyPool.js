const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class ProxyPool {
    constructor(config = {}) {
        this.config = {
            // 代理获取配置
            proxyUrl: config.proxyUrl || null, // 获取代理的API地址
            proxyFetchInterval: config.proxyFetchInterval || 11000, // 代理请求间隔（毫秒），默认11秒
            
            // 静态代理配置
            staticProxy: config.staticProxy || null, // 静态代理配置
            
            // 代理有效期配置（分钟）
            proxyValidityDuration: config.proxyValidityDuration || 8, // 8分钟有效期
            
            // 代理验证配置
            validateTimeout: config.validateTimeout || 10000, // 10秒验证超时
            validateUrl: config.validateUrl || 'https://www.baidu.com/', // 验证URL
            
            // 清理配置
            cleanupInterval: config.cleanupInterval || 60000, // 1分钟清理一次过期代理
            
            // 持久化配置
            stateFile: config.stateFile || './proxy_pool_state.json', // 代理池状态文件
            autoSave: config.autoSave !== false, // 是否自动保存状态，默认开启
            saveInterval: config.saveInterval || 30000, // 自动保存间隔（毫秒）
        };
        
        // 代理池状态
        this.proxies = new Map(); // Map<proxyId, ProxyInfo>
        this.assignedProxies = new Map(); // Map<tokenId, proxyId> - token到代理的映射
        this.proxyIdCounter = 0;
        this.staticProxyId = null; // 静态代理ID
        this.lastProxyFetchTime = 0; // 上次获取代理的时间戳
        
        // 定时器
        this.cleanupTimer = null;
        this.saveTimer = null;
        
        // 加载已保存的代理状态
        this.loadProxyState();
        
        // 初始化静态代理
        this.initStaticProxy();
        
        // 启动定时清理
        this.startCleanupTimer();
        
        // 启动自动保存
        if (this.config.autoSave) {
            this.startAutoSave();
        }
        
        console.log('🏊 代理池已初始化');
        console.log(`   - 代理有效期: ${this.config.proxyValidityDuration} 分钟`);
        console.log(`   - 代理请求间隔: ${this.config.proxyFetchInterval / 1000} 秒`);
        console.log(`   - 按需分配: 根据活跃token数量动态管理`);
        console.log(`   - 静态代理: ${this.config.staticProxy ? '已配置' : '未配置'}`);
        console.log(`   - 动态代理API: ${this.config.proxyUrl ? '已配置' : '未配置'}`);
        
        // 判断并显示代理分配模式
        const isStaticOnlyMode = this.config.staticProxy && !this.config.proxyUrl;
        if (isStaticOnlyMode) {
            console.log(`   - 分配模式: 静态代理独享模式 (所有token共享静态代理)`);
        } else if (this.config.staticProxy && this.config.proxyUrl) {
            console.log(`   - 分配模式: 混合模式 (优先静态代理，需要时使用动态代理)`);
        } else if (this.config.proxyUrl) {
            console.log(`   - 分配模式: 动态代理模式 (仅使用动态代理)`);
        } else {
            console.log(`   - 分配模式: 未配置代理`);
        }
        
        console.log(`   - 状态文件: ${this.config.stateFile}`);
        console.log(`   - 已恢复代理: ${this.proxies.size} 个`);
        
        // 显示距离下次可以请求代理的时间
        if (this.lastProxyFetchTime > 0) {
            const now = Date.now();
            const timeSinceLastFetch = now - this.lastProxyFetchTime;
            const remainingTime = Math.max(0, this.config.proxyFetchInterval - timeSinceLastFetch);
            if (remainingTime > 0) {
                console.log(`   - 下次可请求代理: ${Math.ceil(remainingTime / 1000)} 秒后`);
            }
        }
    }
    
    // 初始化静态代理
    initStaticProxy() {
        if (!this.config.staticProxy) {
            return;
        }
        
        try {
            const { host, port, protocol = 'http', auth } = this.config.staticProxy;
            
            if (!host || !port) {
                console.error('❌ 静态代理配置不完整，需要 host 和 port');
                return;
            }
            
            // 构建代理字符串
            let proxyString;
            if (auth && auth.username && auth.password) {
                proxyString = `${auth.username}:${auth.password}@${host}:${port}`;
            } else {
                proxyString = `${host}:${port}`;
            }
            
            // 检查是否已存在静态代理
            const existingStaticProxy = Array.from(this.proxies.values()).find(proxy => 
                proxy.proxyString === proxyString && proxy.isStatic
            );
            
            if (existingStaticProxy) {
                this.staticProxyId = existingStaticProxy.id;
                console.log(`🔧 发现现有静态代理[${existingStaticProxy.id}]: ${proxyString}`);
                return;
            }
            
            // 创建静态代理信息
            const proxyInfo = this.createProxyInfo(proxyString);
            if (proxyInfo) {
                proxyInfo.isStatic = true; // 标记为静态代理
                proxyInfo.status = 'active'; // 静态代理默认为活跃状态
                proxyInfo.validatedAt = new Date();
                proxyInfo.expiresAt = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 静态代理1年有效期
                
                this.proxies.set(proxyInfo.id, proxyInfo);
                this.staticProxyId = proxyInfo.id;
                
                console.log(`🔧 已添加静态代理[${proxyInfo.id}]: ${proxyString}`);
                
                // 保存状态
                if (this.config.autoSave) {
                    this.saveProxyState();
                }
            }
            
        } catch (error) {
            console.error('❌ 初始化静态代理失败:', error.message);
        }
    }
    
    // 代理信息结构
    createProxyInfo(proxyString) {
        const proxyId = ++this.proxyIdCounter;
        const proxyUrl = this.parseProxyString(proxyString);
        
        if (!proxyUrl) {
            return null;
        }
        
        return {
            id: proxyId,
            proxyString: proxyString.trim(),
            proxyUrl: proxyUrl,
            status: 'pending', // pending, active, invalid, expired
            createdAt: new Date(),
            validatedAt: null,
            expiresAt: null,
            assignedTo: null, // tokenId
            assignedAt: null,
            lastError: null,
            retryCount: 0,
            isStatic: false // 是否为静态代理
        };
    }
    
    // 解析代理字符串
    parseProxyString(proxyString) {
        try {
            const match = proxyString.trim().match(/^(?:([^:]+):([^@]+)@)?([^:]+):(\d+)$/);
            if (!match) {
                console.error('❌ 代理格式错误，期望格式: user:pass@ip:port 或 ip:port');
                return null;
            }
            
            const [, username, password, host, port] = match;
            
            // 构建代理URL
            if (username && password) {
                return `http://${username}:${password}@${host}:${port}`;
            } else {
                return `http://${host}:${port}`;
            }
        } catch (error) {
            console.error('❌ 解析代理字符串失败:', error.message);
            return null;
        }
    }
    
    // 使用curl验证代理有效性
    async validateProxy(proxyInfo) {
        try {
            console.log(`🔍 验证代理[${proxyInfo.id}]: ${proxyInfo.proxyString}`);
            
            const curlCommand = `curl -v -x "${proxyInfo.proxyUrl}" "${this.config.validateUrl}" --connect-timeout 10 --max-time ${this.config.validateTimeout / 1000}`;
            
            const { stdout, stderr } = await execAsync(curlCommand);
            
            // 检查curl输出，判断是否成功
            if (stderr.includes('HTTP/1.1 200') || stderr.includes('HTTP/2 200') || stdout.length > 0) {
                // 验证成功
                proxyInfo.status = 'active';
                proxyInfo.validatedAt = new Date();
                proxyInfo.expiresAt = new Date(Date.now() + this.config.proxyValidityDuration * 60 * 1000);
                proxyInfo.lastError = null;
                proxyInfo.retryCount = 0;
                
                console.log(`✅ 代理[${proxyInfo.id}]验证成功，有效期至 ${proxyInfo.expiresAt.toLocaleString()}`);
                return true;
            } else {
                throw new Error('代理连接失败或响应异常');
            }
            
        } catch (error) {
            // 验证失败
            proxyInfo.status = 'invalid';
            proxyInfo.lastError = error.message;
            proxyInfo.retryCount++;
            
            console.log(`❌ 代理[${proxyInfo.id}]验证失败`);
            return false;
        }
    }
    
    // 从API获取新代理
    async fetchNewProxy() {
        if (!this.config.proxyUrl) {
            throw new Error('未配置代理获取URL');
        }
        
        const now = Date.now();
        const timeSinceLastFetch = now - this.lastProxyFetchTime;
        
        // 检查是否需要等待
        if (timeSinceLastFetch < this.config.proxyFetchInterval) {
            const remainingTime = this.config.proxyFetchInterval - timeSinceLastFetch;
            console.log(`⏳ 代理请求间隔限制，需要等待 ${Math.ceil(remainingTime / 1000)} 秒后才能获取新代理`);
            return null;
        }
        
        try {
            console.log('🌐 正在获取新代理...');
            
            // 更新上次请求时间
            this.lastProxyFetchTime = now;
            
            const response = await axios.get(this.config.proxyUrl, { 
                timeout: 10000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                }
            });
            
            if (response.data && typeof response.data === 'string') {
                const proxyString = response.data.trim();
                const proxyInfo = this.createProxyInfo(proxyString);
                
                if (proxyInfo) {
                    // 立即验证新获取的代理
                    const isValid = await this.validateProxy(proxyInfo);
                    if (isValid) {
                        this.proxies.set(proxyInfo.id, proxyInfo);
                        console.log(`✅ 成功添加新代理[${proxyInfo.id}]到池中`);
                        
                        // 保存状态
                        if (this.config.autoSave) {
                            this.saveProxyState();
                        }
                        
                        return proxyInfo;
                    } else {
                        console.log(`❌ 新获取的代理验证失败，丢弃`);
                        return null;
                    }
                } else {
                    console.error('❌ 代理格式解析失败');
                    return null;
                }
            } else {
                console.error('❌ 代理API响应格式错误:', response.data);
                return null;
            }
            
        } catch (error) {
            console.error('❌ 获取新代理失败:', error.message);
            return null;
        }
    }
    
    // 获取活跃的代理列表
    getActiveProxies() {
        const now = new Date();
        return Array.from(this.proxies.values()).filter(proxy => 
            proxy.status === 'active' && 
            proxy.expiresAt && 
            proxy.expiresAt > now
        );
    }
    
    // 获取可用的代理（未分配的活跃代理）
    getAvailableProxies() {
        return this.getActiveProxies().filter(proxy => !proxy.assignedTo);
    }
    
    // 为token分配代理
    async assignProxyToToken(tokenId) {
        // 检查token是否已经有代理
        if (this.assignedProxies.has(tokenId)) {
            const existingProxyId = this.assignedProxies.get(tokenId);
            const existingProxy = this.proxies.get(existingProxyId);
            
            if (existingProxy && existingProxy.status === 'active' && existingProxy.expiresAt > new Date()) {
                // 现有代理仍然有效，静态代理不需要重新验证
                if (existingProxy.isStatic) {
                    console.log(`🔧 Token[${tokenId}]使用现有静态代理[${existingProxy.id}]`);
                    return {
                        proxyUrl: existingProxy.proxyUrl,
                        isStatic: true,
                        proxyId: existingProxy.id,
                        proxyString: existingProxy.proxyString
                    };
                } else {
                    // 动态代理需要重新验证
                    console.log(`🔍 重新验证Token[${tokenId}]的现有代理[${existingProxy.id}]`);
                    const isValid = await this.validateProxy(existingProxy);
                    if (isValid) {
                        return {
                            proxyUrl: existingProxy.proxyUrl,
                            isStatic: false,
                            proxyId: existingProxy.id,
                            proxyString: existingProxy.proxyString
                        };
                    } else {
                        // 验证失败，释放并重新分配
                        console.log(`❌ Token[${tokenId}]的现有代理验证失败，将重新分配`);
                        this.releaseProxyFromToken(tokenId);
                    }
                }
            } else {
                // 现有代理已失效，需要重新分配
                this.releaseProxyFromToken(tokenId);
            }
        }
        
        // 检查是否只有静态代理配置（没有动态代理API）
        const isStaticOnlyMode = this.staticProxyId && !this.config.proxyUrl;
        
        // 优先使用静态代理，如果是静态代理独享模式则强制使用
        if (this.staticProxyId) {
            const staticProxy = this.proxies.get(this.staticProxyId);
            if (staticProxy && staticProxy.status === 'active') {
                // 如果是静态代理独享模式，允许多个token共享同一个静态代理
                if (isStaticOnlyMode || !staticProxy.assignedTo) {
                    // 记录分配关系（静态代理独享模式下可能有多个token使用同一个代理）
                    if (!staticProxy.assignedTo) {
                        staticProxy.assignedTo = tokenId;
                        staticProxy.assignedAt = new Date();
                    }
                    this.assignedProxies.set(tokenId, staticProxy.id);
                    
                    console.log(`🔧 已将静态代理[${staticProxy.id}]分配给Token[${tokenId}]: ${staticProxy.proxyString}${isStaticOnlyMode ? ' (静态代理独享模式)' : ''}`);
                    
                    // 保存状态
                    if (this.config.autoSave) {
                        this.saveProxyState();
                    }
                    
                    return {
                        proxyUrl: staticProxy.proxyUrl,
                        isStatic: true,
                        proxyId: staticProxy.id,
                        proxyString: staticProxy.proxyString
                    };
                }
            }
        }
        
        // 如果是静态代理独享模式但静态代理不可用，返回错误
        if (isStaticOnlyMode) {
            console.log(`❌ 静态代理独享模式下，静态代理不可用，无法为Token[${tokenId}]分配代理`);
            return null;
        }
        
        // 静态代理不可用，尝试从现有可用代理中分配
        let availableProxies = this.getAvailableProxies().sort((a, b) => 
            new Date(b.validatedAt) - new Date(a.validatedAt)
        );
        
        // 逐个验证现有代理直到找到有效的
        let selectedProxy = null;
        for (const proxy of availableProxies) {
            console.log(`🔍 分配前验证现有代理[${proxy.id}]: ${proxy.proxyString}`);
            const isValid = await this.validateProxy(proxy);
            
            if (isValid) {
                selectedProxy = proxy;
                break;
            } else {
                // 验证失败，标记为无效
                console.log(`❌ 代理[${proxy.id}]验证失败，标记为无效`);
                proxy.status = 'invalid';
            }
        }
        
        // 如果没有可用的现有代理，则获取新代理
        if (!selectedProxy) {
            console.log(`🔄 没有可用的现有代理，为Token[${tokenId}]获取新代理`);
            
            // 清理无效代理
            this.cleanupExpiredProxies();
            
            // 获取新代理
            const newProxy = await this.fetchNewProxy();
            if (newProxy) {
                selectedProxy = newProxy;
            } else {
                console.log(`❌ 无法为Token[${tokenId}]获取新代理`);
                return null;
            }
        }
        
        // 分配代理
        selectedProxy.assignedTo = tokenId;
        selectedProxy.assignedAt = new Date();
        this.assignedProxies.set(tokenId, selectedProxy.id);
        
        console.log(`🔗 已将代理[${selectedProxy.id}]分配给Token[${tokenId}]: ${selectedProxy.proxyString}`);
        
        // 保存状态
        if (this.config.autoSave) {
            this.saveProxyState();
        }
        
        return {
            proxyUrl: selectedProxy.proxyUrl,
            isStatic: false,
            proxyId: selectedProxy.id,
            proxyString: selectedProxy.proxyString
        };
    }
    
    // 从token释放代理（当token被封禁或不再需要时）
    releaseProxyFromToken(tokenId) {
        if (!this.assignedProxies.has(tokenId)) {
            return false;
        }
        
        const proxyId = this.assignedProxies.get(tokenId);
        const proxy = this.proxies.get(proxyId);
        
        if (proxy) {
            // 检查是否是静态代理独享模式
            const isStaticOnlyMode = this.staticProxyId && !this.config.proxyUrl;
            const isStaticProxy = proxy.isStatic;
            
            // 静态代理在独享模式下不完全释放assignedTo，以便其他token可以继续使用
            if (isStaticProxy && isStaticOnlyMode) {
                // 静态代理独享模式下，只从assignedProxies中移除token映射，不清除代理的assignedTo
                console.log(`🔓 已释放Token[${tokenId}]的静态代理[${proxyId}] (静态代理独享模式，代理仍可供其他token使用)`);
            } else {
                // 普通模式下完全释放代理
                proxy.assignedTo = null;
                proxy.assignedAt = null;
                console.log(`🔓 已释放Token[${tokenId}]的代理[${proxyId}]`);
            }
        }
        
        this.assignedProxies.delete(tokenId);
        
        // 保存状态
        if (this.config.autoSave) {
            this.saveProxyState();
        }
        
        return true;
    }
    
    // 重新分配代理（当token被封禁时，将其代理分配给下一个需要的token）
    async reassignProxyFromBannedToken(bannedTokenId, newTokenId) {
        if (!this.assignedProxies.has(bannedTokenId)) {
            return false;
        }
        
        const proxyId = this.assignedProxies.get(bannedTokenId);
        const proxy = this.proxies.get(proxyId);
        
        if (!proxy || proxy.status !== 'active' || proxy.expiresAt <= new Date()) {
            // 代理已失效，直接释放
            this.releaseProxyFromToken(bannedTokenId);
            return false;
        }
        
        // 检查新token是否已有代理
        if (this.assignedProxies.has(newTokenId)) {
            console.log(`⚠️ Token[${newTokenId}]已有代理，无法重新分配`);
            return false;
        }
        
        // 重新分配
        this.assignedProxies.delete(bannedTokenId);
        proxy.assignedTo = newTokenId;
        proxy.assignedAt = new Date();
        this.assignedProxies.set(newTokenId, proxyId);
        
        console.log(`🔄 已将代理[${proxyId}]从被封禁的Token[${bannedTokenId}]重新分配给Token[${newTokenId}]`);
        
        // 保存状态
        if (this.config.autoSave) {
            this.saveProxyState();
        }
        
        return true;
    }
    
    // 清理过期和无效的代理
    cleanupExpiredProxies() {
        const now = new Date();
        let cleanedCount = 0;
        
        for (const [proxyId, proxy] of this.proxies.entries()) {
            let shouldRemove = false;
            
            // 跳过静态代理的清理
            if (proxy.isStatic) {
                continue;
            }
            
            // 清理过期的代理
            if (proxy.expiresAt && proxy.expiresAt <= now) {
                console.log(`🗑️ 清理过期代理[${proxyId}]: ${proxy.proxyString}`);
                shouldRemove = true;
            }
            
            // 清理无效的代理
            if (proxy.status === 'invalid') {
                console.log(`🗑️ 清理无效代理[${proxyId}]: ${proxy.proxyString}`);
                shouldRemove = true;
            }
            
            if (shouldRemove) {
                // 如果代理正在被使用，先释放
                if (proxy.assignedTo) {
                    this.assignedProxies.delete(proxy.assignedTo);
                }
                
                this.proxies.delete(proxyId);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            console.log(`🧹 已清理 ${cleanedCount} 个过期/无效代理`);
            
            // 保存状态
            if (this.config.autoSave) {
                this.saveProxyState();
            }
        }
        
        return cleanedCount;
    }
    
    // 启动定时清理
    startCleanupTimer() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        
        this.cleanupTimer = setInterval(() => {
            this.cleanupExpiredProxies();
            // 只清理过期和无效代理，不主动补充代理池
            // 代理将在token需要时按需获取
        }, this.config.cleanupInterval);
    }
    
    // 停止定时器
    stopTimers() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        
        if (this.saveTimer) {
            clearInterval(this.saveTimer);
            this.saveTimer = null;
        }
    }
    
    // 获取活跃token统计
    getActiveTokensInfo() {
        const now = new Date();
        const activeAssignments = Array.from(this.assignedProxies.entries()).filter(([tokenId, proxyId]) => {
            const proxy = this.proxies.get(proxyId);
            return proxy && proxy.status === 'active' && proxy.expiresAt > now;
        });
        
        return {
            activeTokenCount: activeAssignments.length,
            totalAssignedTokens: this.assignedProxies.size,
            activeAssignments: activeAssignments.map(([tokenId, proxyId]) => {
                const proxy = this.proxies.get(proxyId);
                return {
                    tokenId,
                    proxyId,
                    proxyString: proxy.proxyString,
                    expiresAt: proxy.expiresAt
                };
            })
        };
    }
    
    // 获取代理池状态信息
    getPoolStatus() {
        const now = new Date();
        const allProxies = Array.from(this.proxies.values());
        const tokenInfo = this.getActiveTokensInfo();
        
        const statusCount = {
            total: allProxies.length,
            active: allProxies.filter(p => p.status === 'active' && p.expiresAt > now).length,
            expired: allProxies.filter(p => p.status === 'active' && p.expiresAt <= now).length,
            invalid: allProxies.filter(p => p.status === 'invalid').length,
            pending: allProxies.filter(p => p.status === 'pending').length,
            assigned: allProxies.filter(p => p.assignedTo).length,
            available: allProxies.filter(p => p.status === 'active' && p.expiresAt > now && !p.assignedTo).length,
            activeTokens: tokenInfo.activeTokenCount,
            totalAssignedTokens: tokenInfo.totalAssignedTokens
        };
        
        return {
            ...statusCount,
            assignments: tokenInfo.activeAssignments
        };
    }
    
    // 打印代理池状态
    printStatus() {
        const status = this.getPoolStatus();
        const staticProxyInfo = this.staticProxyId ? this.proxies.get(this.staticProxyId) : null;
        
        console.log(`\n📊 代理池状态 (按需分配模式):`);
        console.log(`   - 代理总计: ${status.total} 个`);
        console.log(`   - 活跃代理: ${status.active} 个`);
        console.log(`   - 可用代理: ${status.available} 个`);
        console.log(`   - 已分配代理: ${status.assigned} 个`);
        console.log(`   - 过期代理: ${status.expired} 个`);
        console.log(`   - 无效代理: ${status.invalid} 个`);
        console.log(`   - 等待验证: ${status.pending} 个`);
        console.log(`   - 活跃Token: ${status.activeTokens} 个`);
        console.log(`   - 总分配Token: ${status.totalAssignedTokens} 个`);
        
        // 显示静态代理信息
        if (staticProxyInfo) {
            const staticStatus = staticProxyInfo.assignedTo ? '已分配' : '可用';
            console.log(`   - 静态代理: [${staticProxyInfo.id}] ${staticProxyInfo.proxyString} (${staticStatus})`);
        } else {
            console.log(`   - 静态代理: 未配置`);
        }
        
        console.log(`   - 状态文件: ${this.config.stateFile}`);
        console.log(`   - 自动保存: ${this.config.autoSave ? '开启' : '关闭'}`);
        
        if (status.assignments.length > 0) {
            console.log(`   - 活跃分配情况:`);
            status.assignments.forEach(assignment => {
                const proxy = this.proxies.get(assignment.proxyId);
                const proxyType = proxy && proxy.isStatic ? '静态' : '动态';
                const expiresIn = assignment.expiresAt ? 
                    Math.max(0, Math.ceil((new Date(assignment.expiresAt) - new Date()) / (1000 * 60))) : 0;
                if (proxy && proxy.isStatic) {
                    console.log(`     • Token[${assignment.tokenId}] -> ${proxyType}代理[${assignment.proxyId}] (长期有效)`);
                } else {
                    console.log(`     • Token[${assignment.tokenId}] -> ${proxyType}代理[${assignment.proxyId}] (${expiresIn}分钟后过期)`);
                }
            });
        }
        
        return status;
    }
    
    // 销毁代理池
    destroy() {
        this.stopTimers();
        
        // 保存最终状态
        if (this.config.autoSave) {
            this.saveProxyState();
        }
        
        // 释放所有分配
        for (const tokenId of this.assignedProxies.keys()) {
            this.releaseProxyFromToken(tokenId);
        }
        
        this.proxies.clear();
        this.assignedProxies.clear();
        
        console.log('🏊 代理池已销毁');
    }
    
    // 加载已保存的代理状态
    loadProxyState() {
        try {
            if (fs.existsSync(this.config.stateFile)) {
                console.log(`📂 正在加载代理池状态: ${this.config.stateFile}`);
                const state = JSON.parse(fs.readFileSync(this.config.stateFile, 'utf-8'));
                
                // 恢复代理数据，转换日期字符串为Date对象
                if (state.proxies && Array.isArray(state.proxies)) {
                    this.proxies = new Map();
                    for (const proxy of state.proxies) {
                        // 转换日期字符串为Date对象
                        if (proxy.createdAt) proxy.createdAt = new Date(proxy.createdAt);
                        if (proxy.validatedAt) proxy.validatedAt = new Date(proxy.validatedAt);
                        if (proxy.expiresAt) proxy.expiresAt = new Date(proxy.expiresAt);
                        if (proxy.assignedAt) proxy.assignedAt = new Date(proxy.assignedAt);
                        
                        this.proxies.set(proxy.id, proxy);
                    }
                }
                
                // 恢复分配关系
                if (state.assignedProxies) {
                    this.assignedProxies = new Map(Object.entries(state.assignedProxies));
                }
                
                // 恢复计数器
                if (state.proxyIdCounter) {
                    this.proxyIdCounter = state.proxyIdCounter;
                }
                
                // 恢复上次代理请求时间
                if (state.lastProxyFetchTime) {
                    this.lastProxyFetchTime = state.lastProxyFetchTime;
                }
                
                // 清理过期的代理
                this.cleanupExpiredProxies();
                
                console.log(`✅ 已恢复代理池状态: ${this.proxies.size} 个代理, ${this.assignedProxies.size} 个分配`);
            } else {
                console.log(`📋 未找到代理池状态文件，将从空池开始`);
            }
        } catch (error) {
            console.error('❌ 加载代理池状态失败:', error.message);
            console.log('📋 将从空池开始');
            // 重置状态
            this.proxies.clear();
            this.assignedProxies.clear();
            this.proxyIdCounter = 0;
        }
    }
    
    // 启动自动保存
    startAutoSave() {
        if (this.saveTimer) {
            clearInterval(this.saveTimer);
        }
        
        this.saveTimer = setInterval(() => {
            this.saveProxyState();
        }, this.config.saveInterval);
    }
    
    // 保存代理池状态
    saveProxyState() {
        try {
            const state = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                proxies: Array.from(this.proxies.values()),
                assignedProxies: Object.fromEntries(this.assignedProxies),
                proxyIdCounter: this.proxyIdCounter,
                lastProxyFetchTime: this.lastProxyFetchTime,
                config: {
                    proxyValidityDuration: this.config.proxyValidityDuration,
                    proxyFetchInterval: this.config.proxyFetchInterval,
                    onDemandAllocation: true, // 按需分配模式
                },
                statistics: {
                    totalProxies: this.proxies.size,
                    activeProxies: this.getActiveProxies().length,
                    assignedProxies: this.assignedProxies.size,
                    availableProxies: this.getAvailableProxies().length
                }
            };
            
            fs.writeFileSync(this.config.stateFile, JSON.stringify(state, null, 2), 'utf-8');
            
        } catch (error) {
            console.error('❌ 保存代理池状态失败:', error.message);
        }
    }
    
    // 手动保存状态
    saveStateNow() {
        console.log('💾 手动保存代理池状态...');
        this.saveProxyState();
        console.log('✅ 代理池状态已保存');
    }
}

module.exports = ProxyPool; 