const fs = require('fs');
const path = require('path');

class SimpleCompletionChecker {
    constructor() {
        this.paramsFile = './params.json';
        this.totalPagesFile = './recalculated_total_pages.json';
        this.baseDataDir = './';
        
        this.params = null;
        this.totalPagesData = null;
        this.completionStats = {
            total: 0,
            completed: 0,
            incomplete: 0
        };
    }

    // 读取配置文件
    loadFiles() {
        try {
            console.log('📖 读取配置文件...');
            
            // 读取参数配置文件
            const paramsContent = fs.readFileSync(this.paramsFile, 'utf8');
            this.params = JSON.parse(paramsContent);
            console.log(`✅ 已读取 ${this.params.length} 个参数组合`);
            
            // 读取总页数文件
            const totalPagesContent = fs.readFileSync(this.totalPagesFile, 'utf8');
            this.totalPagesData = JSON.parse(totalPagesContent);
            console.log(`✅ 已读取总页数统计文件`);
            
        } catch (error) {
            console.error('❌ 读取配置文件失败:', error.message);
            throw error;
        }
    }

    // 清理目录名
    sanitizeDirName(name) {
        if (!name) return 'unknown';
        return name.replace(/[<>:"/\\|?*]/g, '_').trim();
    }

    // 生成目录路径
    generateDirectoryPath(combination) {
        return path.join(
            this.baseDataDir,
            this.sanitizeDirName(combination.studyPhaseName),
            this.sanitizeDirName(combination.subjectName),
            this.sanitizeDirName(combination.textbookVersionName),
            this.sanitizeDirName(combination.ceciName),
            this.sanitizeDirName(combination.catalogName)
        );
    }

    // 获取目录中最大的页码文件编号
    getMaxPageNumber(dirPath) {
        try {
            if (!fs.existsSync(dirPath)) {
                return 0;
            }
            
            const files = fs.readdirSync(dirPath);
            const pageNumbers = files
                .filter(file => /^\d+\.json$/.test(file))
                .map(file => parseInt(file.replace('.json', '')))
                .filter(num => !isNaN(num));
            
            return pageNumbers.length > 0 ? Math.max(...pageNumbers) : 0;
        } catch (error) {
            console.warn(`⚠️ 无法读取目录 ${dirPath}: ${error.message}`);
            return 0;
        }
    }

    // 从processedResults中查找对应的totalPage
    findTotalPageFromProcessedResults(combination) {
        if (!this.totalPagesData.processedResults) {
            return null;
        }

        const result = this.totalPagesData.processedResults.find(item => 
            item.studyPhaseName === combination.studyPhaseName &&
            item.subjectName === combination.subjectName &&
            item.textbookVersionName === combination.textbookVersionName &&
            item.ceciName === combination.ceciName &&
            item.catalogName === combination.catalogName
        );

        return result ? result.totalPage : null;
    }

    // 检查单个参数组合的完成情况
    checkCombinationCompletion(combination, index) {
        const dirPath = this.generateDirectoryPath(combination);
        const maxPageNumber = this.getMaxPageNumber(dirPath);
        const expectedTotalPage = this.findTotalPageFromProcessedResults(combination);
        
        let isComplete = false;
        let status = '';

        if (expectedTotalPage === null) {
            status = `未找到totalPage数据`;
        } else if (maxPageNumber === 0) {
            status = `未开始 (0/${expectedTotalPage})`;
        } else if (maxPageNumber === expectedTotalPage) {
            status = `完成 (${maxPageNumber}/${expectedTotalPage})`;
            isComplete = true;
        } else {
            status = `进行中 (${maxPageNumber}/${expectedTotalPage})`;
        }

        // 如果完成了，添加isDone字段
        if (isComplete && !combination.isDone) {
            combination.isDone = true;
        }

        // 打印进度（每100个组合打印一次）
        if ((index + 1) % 100 === 0) {
            console.log(`🔍 已检查 ${index + 1}/${this.params.length} 个参数组合...`);
        }

        return { isComplete, status, maxPageNumber, expectedTotalPage, dirPath };
    }

    // 检查所有参数组合的完成情况
    checkAllCombinations() {
        console.log('\n🔍 开始检查所有参数组合的完成情况...');
        
        this.completionStats.total = this.params.length;
        const details = [];
        
        for (let i = 0; i < this.params.length; i++) {
            const combination = this.params[i];
            const result = this.checkCombinationCompletion(combination, i);
            
            if (result.isComplete) {
                this.completionStats.completed++;
            } else {
                this.completionStats.incomplete++;
            }

            details.push({
                index: i + 1,
                combination: `${combination.studyPhaseName}/${combination.subjectName}/${combination.textbookVersionName}/${combination.ceciName}/${combination.catalogName}`,
                status: result.status,
                isComplete: result.isComplete,
                maxPage: result.maxPageNumber,
                expectedPage: result.expectedTotalPage,
                dirPath: result.dirPath
            });
        }
        
        console.log(`✅ 检查完成！总计: ${this.completionStats.total}, 已完成: ${this.completionStats.completed}, 未完成: ${this.completionStats.incomplete}`);
        
        return details;
    }

    // 保存更新后的参数文件
    saveUpdatedParams() {
        try {
            // 备份原文件
            const backupFile = `${this.paramsFile}.backup.${Date.now()}`;
            fs.copyFileSync(this.paramsFile, backupFile);
            console.log(`📋 已备份原文件到: ${backupFile}`);
            
            // 保存更新后的文件
            fs.writeFileSync(this.paramsFile, JSON.stringify(this.params, null, 2), 'utf8');
            console.log(`💾 已更新参数文件: ${this.paramsFile}`);
            
        } catch (error) {
            console.error('❌ 保存参数文件失败:', error.message);
            throw error;
        }
    }

    // 显示统计摘要
    showSummary() {
        console.log('\n📊 完成情况统计:');
        console.log('='.repeat(60));
        console.log(`总参数组合数: ${this.completionStats.total}`);
        console.log(`已完成: ${this.completionStats.completed} (${((this.completionStats.completed / this.completionStats.total) * 100).toFixed(1)}%)`);
        console.log(`未完成: ${this.completionStats.incomplete} (${((this.completionStats.incomplete / this.completionStats.total) * 100).toFixed(1)}%)`);
        console.log('='.repeat(60));
    }

    // 主运行方法
    async run() {
        try {
            console.log('🚀 开始检查爬取完成情况...\n');
            
            // 读取配置文件
            this.loadFiles();
            
            // 检查所有参数组合
            const details = this.checkAllCombinations();
            
            // 保存更新后的参数文件
            if (this.completionStats.completed > 0) {
                this.saveUpdatedParams();
                console.log(`✅ 已为 ${this.completionStats.completed} 个完成的参数组合添加 isDone 标记`);
            } else {
                console.log('ℹ️ 没有发现完成的参数组合，无需更新参数文件');
            }
            
            // 显示统计摘要
            this.showSummary();
            
            // 保存详细报告
            const reportFile = `./completion-report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
            const report = {
                generatedAt: new Date().toISOString(),
                summary: this.completionStats,
                details: details.filter(d => d.isComplete).slice(0, 20), // 只保存前20个完成的详情
                incompleteCount: details.filter(d => !d.isComplete).length
            };
            
            fs.writeFileSync(reportFile, JSON.stringify(report, null, 2), 'utf8');
            console.log(`📊 详细报告已保存到: ${reportFile}`);
            
            console.log('\n🎊 检查完成！');
            
        } catch (error) {
            console.error('❌ 检查过程中发生错误:', error.message);
            process.exit(1);
        }
    }
}

// 运行脚本
async function main() {
    const checker = new SimpleCompletionChecker();
    await checker.run();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = SimpleCompletionChecker; 