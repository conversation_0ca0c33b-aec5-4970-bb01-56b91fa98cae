#!/usr/bin/env node

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 执行命令的Promise包装器
 * @param {string} command - 要执行的命令
 * @returns {Promise} - 命令执行结果
 */
function execCommand(command) {
    return new Promise((resolve, reject) => {
        console.log(`🚀 执行命令: ${command}`);
        
        const process = exec(command, (error, stdout, stderr) => {
            if (error) {
                reject(error);
            } else {
                resolve({ stdout, stderr });
            }
        });

        // 实时输出命令结果
        process.stdout.on('data', (data) => {
            process.stdout.write(data);
        });

        process.stderr.on('data', (data) => {
            process.stderr.write(data);
        });
    });
}

/**
 * 检查必要的文件是否存在
 */
function checkRequiredFiles() {
    const requiredFiles = [
        './phone_list.json',
        './testTokens.js',
        './reLogin.js'
    ];

    console.log('🔍 检查必要文件...');
    
    const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
    
    if (missingFiles.length > 0) {
        console.error(`❌ 缺少必要文件: ${missingFiles.join(', ')}`);
        process.exit(1);
    }
    
    console.log('✅ 所有必要文件都存在');
}

/**
 * 显示文件统计信息
 */
function showFileStats() {
    try {
        // 显示 phone_list.json 统计
        if (fs.existsSync('./phone_list.json')) {
            const phoneList = JSON.parse(fs.readFileSync('./phone_list.json', 'utf8'));
            const totalPhones = phoneList.phones ? phoneList.phones.length : 0;
            const validPhones = phoneList.phones ? phoneList.phones.filter(p => p.tokenValid !== false).length : 0;
            const invalidPhones = totalPhones - validPhones;
            
            console.log(`\n📊 手机号统计:`);
            console.log(`   📱 总数: ${totalPhones}`);
            console.log(`   ✅ 有效/未测试: ${validPhones}`);
            console.log(`   ❌ 无效: ${invalidPhones}`);
        }

        // 显示 tokens.json 统计
        if (fs.existsSync('./tokens.json')) {
            const tokens = JSON.parse(fs.readFileSync('./tokens.json', 'utf8'));
            const tokenCount = tokens.tokens ? tokens.tokens.length : 0;
            console.log(`   🪙 现有Token: ${tokenCount}`);
            
            if (tokens.lastUpdated) {
                console.log(`   📅 最后更新: ${new Date(tokens.lastUpdated).toLocaleString()}`);
            }
        }
    } catch (error) {
        console.warn('⚠️ 读取文件统计信息失败:', error.message);
    }
}

/**
 * 主函数
 */
async function main() {
    const args = process.argv.slice(2);
    
    console.log('🎯 Token 测试和重新登录工具\n');
    
    // 检查必要文件
    checkRequiredFiles();
    
    // 显示当前状态
    showFileStats();
    
    try {
        // 解析命令行参数
        const testOnly = args.includes('--test-only') || args.includes('-t');
        const reloginOnly = args.includes('--relogin-only') || args.includes('-r');
        const force = args.includes('--force') || args.includes('-f');
        
        if (testOnly && reloginOnly) {
            console.error('❌ 不能同时使用 --test-only 和 --relogin-only 选项');
            process.exit(1);
        }
        
        if (testOnly) {
            console.log('\n🔍 仅执行Token有效性测试...\n');
            await execCommand('node testTokens.js');
        } else if (reloginOnly) {
            console.log('\n🔄 仅执行重新登录...\n');
            await execCommand('node reLogin.js');
        } else {
            // 完整流程
            console.log('\n📋 执行完整流程: 测试Token有效性 → 重新登录\n');
            
            // 步骤1: 测试token有效性
            console.log('🔍 步骤1: 测试Token有效性...\n');
            try {
                await execCommand('node testTokens.js');
                console.log('\n✅ Token有效性测试完成\n');
            } catch (error) {
                console.error('\n❌ Token测试失败:', error.message);
                if (!force) {
                    console.log('💡 使用 --force 选项可以忽略测试失败继续重新登录');
                    process.exit(1);
                }
                console.log('⚠️ 忽略测试失败，继续重新登录...\n');
            }
            
            // 步骤2: 重新登录
            console.log('🔄 步骤2: 重新登录...\n');
            try {
                await execCommand('node reLogin.js');
                console.log('\n✅ 重新登录完成');
            } catch (error) {
                console.error('\n❌ 重新登录失败:', error.message);
                process.exit(1);
            }
        }
        
        // 显示最终统计
        console.log('\n' + '='.repeat(50));
        console.log('🎉 执行完成！最终统计:');
        showFileStats();
        console.log('='.repeat(50));
        
    } catch (error) {
        console.error('\n❌ 执行过程中发生错误:', error.message);
        process.exit(1);
    }
}

/**
 * 显示帮助信息
 */
function showHelp() {
    console.log(`
🎯 Token 测试和重新登录工具

使用方法:
  node runTokenFlow.js [选项]

选项:
  -t, --test-only      仅执行Token有效性测试
  -r, --relogin-only   仅执行重新登录
  -f, --force          忽略测试失败继续执行
  -h, --help           显示此帮助信息

示例:
  node runTokenFlow.js                # 完整流程: 测试 → 重新登录
  node runTokenFlow.js --test-only    # 仅测试Token有效性
  node runTokenFlow.js --relogin-only # 仅重新登录
  node runTokenFlow.js --force        # 忽略测试失败继续执行

说明:
  - 默认执行完整流程：先测试Token有效性，再重新登录
  - 测试会在 phone_list.json 中标记无效的Token
  - 重新登录会跳过已标记为无效的手机号
  - 使用 --force 可以在Token测试失败时继续执行重新登录
`);
}

// 检查是否请求帮助
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    process.exit(0);
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
    process.exit(1);
});

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 脚本执行失败:', error);
        process.exit(1);
    });
} 