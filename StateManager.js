const fs = require('fs');
const path = require('path');

/**
 * 分布式状态管理器
 * 负责原子化地管理所有爬取任务的状态，确保多实例协作时数据一致性。
 */
class StateManager {
    /**
     * @param {object} options 配置选项
     * @param {string} options.progressFile 进度文件的路径
     * @param {string} options.paramsFile 参数组合文件的路径
     * @param {string} options.instanceId 当前运行实例的ID
     */
    constructor(options = {}) {
        this.progressFile = options.progressFile || './progress.json';
        this.paramsFile = options.paramsFile;
        this.instanceId = options.instanceId;
        this.lockFile = `${this.progressFile}.lock`;
        this.lockTimeout = 30000; // 30秒锁超时

        // 构造时，仅确保进度文件存在。同步操作由新的 syncWithParams 方法负责。
        this._ensureProgressFileExists();
    }

    /**
     * [内部方法] 确保进度文件存在。如果不存在，则根据参数文件创建。
     * 这个方法是同步的，只在初始化时执行。
     */
    _ensureProgressFileExists() {
        if (fs.existsSync(this.progressFile)) {
            console.log(`[${this.instanceId}] ✅ 成功加载现有进度文件: ${this.progressFile}`);
            return;
        }

        console.log(`[${this.instanceId}] 📋 未找到进度文件，将根据 ${this.paramsFile} 创建...`);
        if (!this.paramsFile || !fs.existsSync(this.paramsFile)) {
            throw new Error(`参数文件不存在，无法创建进度文件: ${this.paramsFile}`);
        }

        const paramsCombinations = JSON.parse(fs.readFileSync(this.paramsFile, 'utf8'));
        const progressData = {
            totalCombinations: paramsCombinations.length,
            combinations: {},
            createdAt: new Date().toISOString(),
            lastUpdated: new Date().toISOString()
        };

        paramsCombinations.forEach(combination => {
            const key = this.getCombinationKey(combination);
            progressData.combinations[key] = {
                status: 'pending', // pending, processing, completed, failed, obsolete
                combination: combination,
                history: []
            };
        });

        fs.writeFileSync(this.progressFile, JSON.stringify(progressData, null, 2), 'utf8');
        console.log(`[${this.instanceId}] ✅ 成功创建了包含 ${paramsCombinations.length} 个任务的进度文件。`);
    }

    /**
     * 【核心同步方法】
     * 在每次执行前调用，用于将进度文件 (progress.json) 与参数文件 (params.json) 完全同步。
     * - 添加新任务。
     * - 将不再存在的任务标记为 'obsolete'。
     * - 保留有效任务的现有状态。
     */
    async syncWithParams() {
        if (!await this.acquireLock()) {
            console.error(`[${this.instanceId}] 无法获取锁，跳过同步操作。`);
            return;
        }

        console.log(`[${this.instanceId}] 🚀 开始同步 progress 和 params 文件...`);
        try {
            if (!this.paramsFile || !fs.existsSync(this.paramsFile)) {
                throw new Error(`参数文件不存在: ${this.paramsFile}`);
            }

            // 1. 读取 params.json 作为“真实数据源”
            const paramsCombinations = JSON.parse(fs.readFileSync(this.paramsFile, 'utf8'));
            const validKeys = new Set(paramsCombinations.map(c => this.getCombinationKey(c)));

            // 2. 读取当前的 progress.json
            const progressData = JSON.parse(fs.readFileSync(this.progressFile, 'utf8'));
            const oldCombinations = progressData.combinations;
            const newCombinations = {};

            let newTasksAdded = 0;
            let tasksMarkedObsolete = 0;

            // 3. 以 params.json 为基准，重建任务列表
            paramsCombinations.forEach(combination => {
                const key = this.getCombinationKey(combination);
                if (oldCombinations[key]) {
                    // 如果任务已存在，保留其状态
                    newCombinations[key] = oldCombinations[key];
                } else {
                    // 如果是新任务，添加并设置为 'pending'
                    newCombinations[key] = {
                        status: 'pending',
                        combination: combination,
                        history: [{
                            status: 'created',
                            instanceId: this.instanceId,
                            timestamp: new Date().toISOString(),
                            reason: '在同步期间被添加'
                        }]
                    };
                    newTasksAdded++;
                }
            });

            // 4. 检查旧任务，标记在 params.json 中已不存在的任务为 'obsolete'
            Object.keys(oldCombinations).forEach(key => {
                if (!validKeys.has(key)) {
                    // 如果一个旧任务不在新的有效key集合中，则标记为废弃
                    if (oldCombinations[key].status !== 'obsolete') {
                        const obsoleteTask = oldCombinations[key];
                        obsoleteTask.status = 'obsolete';
                        obsoleteTask.history.push({
                            status: 'obsolete',
                            instanceId: this.instanceId,
                            timestamp: new Date().toISOString(),
                            reason: '在同步时发现任务已从参数文件中移除'
                        });
                        newCombinations[key] = obsoleteTask; // 重新加回列表，但状态已是废弃
                        tasksMarkedObsolete++;
                    } else {
                        // 如果本来就是废弃，直接保留
                         newCombinations[key] = oldCombinations[key];
                    }
                }
            });

            // 5. 更新并写入文件
            progressData.combinations = newCombinations;
            progressData.totalCombinations = paramsCombinations.length; // 总数以 params 文件为准
            progressData.lastUpdated = new Date().toISOString();

            fs.writeFileSync(this.progressFile, JSON.stringify(progressData, null, 2), 'utf8');
            console.log(`[${this.instanceId}] ✅ 同步完成! 新增任务: ${newTasksAdded}, 标记过时: ${tasksMarkedObsolete}.`);

        } catch (error) {
            console.error(`[${this.instanceId}] ❌ 同步进度文件时出错: ${error.message}`);
        } finally {
            this.releaseLock();
        }
    }

    /**
     * 为参数组合生成唯一的键。
     * @param {object} combination 参数组合
     * @returns {string} 唯一键
     */
    getCombinationKey(combination) {
        return Object.values(combination).join('_').replace(/[<>:"/\\|?*]/g, '');
    }
    
    /**
     * 从中心化进度文件中获取下一个待处理的任务。
     * 它现在假设 syncWithParams 已经被调用，所以不再需要检查 key 的有效性。
     * @returns {object|null} 返回一个待处理的参数组合，如果没有则返回 null。
     */
    async getNextCombination() {
        if (!await this.acquireLock()) return null;

        try {
            const progressData = JSON.parse(fs.readFileSync(this.progressFile, 'utf8'));
            const combinations = progressData.combinations;

            let targetKey = null;

            // 优先查找 'pending' 状态的任务
            targetKey = Object.keys(combinations).find(key => combinations[key].status === 'pending');

            // 如果没有 'pending'，查找 'failed' 状态的任务进行重试
            if (!targetKey) {
                targetKey = Object.keys(combinations).find(key => combinations[key].status === 'failed');
            }

            if (targetKey) {
                const task = combinations[targetKey];
                task.status = 'processing';
                task.history.push({
                    status: 'processing',
                    instanceId: this.instanceId,
                    timestamp: new Date().toISOString()
                });
                
                progressData.lastUpdated = new Date().toISOString();
                fs.writeFileSync(this.progressFile, JSON.stringify(progressData, null, 2), 'utf8');
                
                console.log(`[${this.instanceId}] 🚚 领到新任务: ${targetKey}`);
                return task.combination;
            }
            
            console.log(`[${this.instanceId}] ℹ️  没有找到待处理 (pending/failed) 的任务。`);
            return null;

        } finally {
            this.releaseLock();
        }
    }

    /**
     * 更新指定任务的状态。
     * @param {object} combination 要更新的参数组合
     * @param {'completed'|'failed'} status 新的状态
     * @param {object} details 包含额外信息的对象，如错误信息
     */
    async updateCombinationStatus(combination, status, details = {}) {
        if (!await this.acquireLock()) return;

        try {
            const key = this.getCombinationKey(combination);
            const progressData = JSON.parse(fs.readFileSync(this.progressFile, 'utf8'));
            
            if (progressData.combinations[key]) {
                const task = progressData.combinations[key];

                // 防止已经完成或废弃的任务被再次修改状态（除非是强制重试）
                if (['completed', 'obsolete'].includes(task.status)) {
                    console.warn(`[${this.instanceId}] ⚠️ 任务 "${key}" 状态为 ${task.status}，不再更新。`);
                    return;
                }

                task.status = status;
                
                const logEntry = {
                    status: status,
                    instanceId: this.instanceId,
                    timestamp: new Date().toISOString(),
                    ...details
                };
                task.history.push(logEntry);

                // 保留最近10条历史记录
                if(task.history.length > 10) {
                    task.history = task.history.slice(-10);
                }

                progressData.lastUpdated = new Date().toISOString();
                fs.writeFileSync(this.progressFile, JSON.stringify(progressData, null, 2), 'utf8');
                
                const logMessage = status === 'completed' ? '✅ 任务完成' : '❌ 任务失败';
                console.log(`[${this.instanceId}] ${logMessage}: ${key}`);
            } else {
                 console.warn(`[${this.instanceId}] ⚠️ 尝试更新一个不存在的任务: ${key}`);
            }
        } finally {
            this.releaseLock();
        }
    }

    // 文件锁相关方法保持不变
    async acquireLock() {
        const startTime = Date.now();
        while (Date.now() - startTime < this.lockTimeout) {
            try {
                if (fs.existsSync(this.lockFile)) {
                    const lockData = JSON.parse(fs.readFileSync(this.lockFile, 'utf8'));
                    if (Date.now() - lockData.timestamp > this.lockTimeout) {
                        console.warn(`[${this.instanceId}] 🔓 发现并清理超时锁: ${lockData.instanceId}`);
                        fs.unlinkSync(this.lockFile);
                    }
                }
                const lockData = { instanceId: this.instanceId, timestamp: Date.now() };
                fs.writeFileSync(this.lockFile, JSON.stringify(lockData), { flag: 'wx' });
                return true;
            } catch (error) {
                if (error.code !== 'EEXIST') console.error(`❌ 获取锁时出错: ${error.message}`);
                await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 100));
            }
        }
        console.error(`[${this.instanceId}] ⏰ 获取进度文件锁超时！`);
        return false;
    }

    releaseLock() {
        try {
            if (fs.existsSync(this.lockFile)) {
                const lockData = JSON.parse(fs.readFileSync(this.lockFile, 'utf8'));
                if (lockData.instanceId === this.instanceId) {
                    fs.unlinkSync(this.lockFile);
                }
            }
        } catch (error) {
            // 在锁释放时，错误通常不关键，警告即可
        }
    }
}

module.exports = StateManager;
