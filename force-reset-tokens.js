#!/usr/bin/env node

/**
 * Force Reset Token Status Script
 * Uses sed to directly modify token file
 */

const { execSync } = require('child_process');
const fs = require('fs');

const TOKENS_FILE = './tokens.json';

function forceResetTokens() {
    try {
        console.log('🔄 Force resetting token status...');
        
        if (!fs.existsSync(TOKENS_FILE)) {
            console.error(`❌ Token file not found: ${TOKENS_FILE}`);
            process.exit(1);
        }

        // Read and parse the file
        const data = JSON.parse(fs.readFileSync(TOKENS_FILE, 'utf8'));
        console.log(`📥 Found ${data.tokens.length} tokens`);

        // Force reset all tokens
        data.tokens.forEach(token => {
            token.isValid = true;
            token.invalidAttempts = 0;
            token.bannedUntil = null;
            token.lastValidated = null;
            token.invalidReason = null;
            token.assignedTo = null;
        });

        // Create a temporary file and then move it
        const tempFile = TOKENS_FILE + '.tmp';
        fs.writeFileSync(tempFile, JSON.stringify(data, null, 2));
        
        // Move temp file to replace original
        execSync(`mv "${tempFile}" "${TOKENS_FILE}"`);
        
        // Verify the change
        const verifyData = JSON.parse(fs.readFileSync(TOKENS_FILE, 'utf8'));
        const validTokens = verifyData.tokens.filter(t => t.isValid === true).length;
        const invalidAttempts = verifyData.tokens.filter(t => t.invalidAttempts > 0).length;
        const bannedTokens = verifyData.tokens.filter(t => t.bannedUntil).length;
        
        console.log(`✅ Reset completed`);
        console.log(`🔍 Verification:`);
        console.log(`   - Valid tokens: ${validTokens}/${data.tokens.length}`);
        console.log(`   - Tokens with invalid attempts: ${invalidAttempts}`);
        console.log(`   - Banned tokens: ${bannedTokens}`);
        
        if (validTokens === data.tokens.length && invalidAttempts === 0 && bannedTokens === 0) {
            console.log('🎉 Token reset completed successfully!');
        } else {
            console.log('⚠️ Token reset may not have been fully successful');
        }
        
    } catch (error) {
        console.error('❌ Failed to reset tokens:', error.message);
        process.exit(1);
    }
}

// Run the reset
forceResetTokens();
