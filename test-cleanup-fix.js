#!/usr/bin/env node

/**
 * 测试清理修复效果的脚本
 */

const ParameterStateManager = require('./src/managers/ParameterStateManager');

async function testCleanupFix() {
    console.log('🧪 测试清理修复效果...\n');

    try {
        // 创建参数状态管理器实例
        const manager = new ParameterStateManager({
            paramsFile: './params.json',
            progressFile: './progress.json',
            maxConcurrentInstances: 10,
            instanceTimeout: 1800000, // 30 minutes
            stateCheckInterval: 120000, // 2 minutes
            lockTimeout: 30000,
            batchSize: 10,
            instanceId: `test_cleanup_${Date.now()}`
        });

        console.log('📊 测试前状态:');
        await manager.loadProgress();
        
        const beforeStats = manager.getStats();
        console.log(`   - 总实例数: ${beforeStats.totalInstances}`);
        console.log(`   - 活跃实例数: ${beforeStats.activeInstances}`);

        // 执行清理
        console.log('\n🧹 执行清理操作...');
        await manager.cleanupStaleProcessing();

        // 检查清理后状态
        console.log('\n📊 测试后状态:');
        const afterStats = manager.getStats();
        console.log(`   - 总实例数: ${afterStats.totalInstances}`);
        console.log(`   - 活跃实例数: ${afterStats.activeInstances}`);

        // 再次执行清理，应该不会有重复的警告
        console.log('\n🔄 再次执行清理（应该没有重复警告）...');
        await manager.cleanupStaleProcessing();

        console.log('\n✅ 测试完成！');

    } catch (error) {
        console.error('❌ 测试过程中出错:', error.message);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    testCleanupFix();
}

module.exports = { testCleanupFix };
