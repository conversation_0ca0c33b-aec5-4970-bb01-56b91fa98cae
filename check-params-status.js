const fs = require('fs');
const path = require('path');

// 加载参数组合文件
function loadParamsCombinations() {
    try {
        const paramsCombinations = require('./params.json');
        console.log(`✅ 已加载参数组合文件: ${paramsCombinations.length} 个组合`);
        return paramsCombinations;
    } catch (error) {
        console.error('❌ 未找到参数组合文件 params.json:', error.message);
        return [];
    }
}

// 清理目录名称（与 PlaywrightCrawler.js 中的 sanitizeDirName 方法一致）
function sanitizeDirName(name) {
    if (!name) return 'unknown';
    return name.replace(/[<>:"/\\|?*]/g, '_').trim();
}

// 生成参数组合对应的输出目录路径
function generateOutputPath(combination) {
    return path.join(
        './',
        sanitizeDirName(combination.studyPhaseName),
        sanitizeDirName(combination.subjectName),
        sanitizeDirName(combination.textbookVersionName),
        sanitizeDirName(combination.ceciName),
        sanitizeDirName(combination.catalogName)
    );
}

// 生成参数组合的唯一标识符（与 PlaywrightCrawler.js 中的 getCombinationKey 方法一致）
function getCombinationKey(combination) {
    return `${combination.studyPhaseCode}_${combination.subjectCode}_${combination.textbookVersionCode}_${combination.ceciCode}_${combination.catalogCode}`;
}

// 检查目录下是否有 JSON 文件
function hasJsonFiles(outputDir) {
    try {
        if (!fs.existsSync(outputDir)) {
            return false;
        }

        const files = fs.readdirSync(outputDir);
        const jsonFiles = files.filter(file => file.match(/^\d+\.json$/));
        
        return jsonFiles.length > 0;
    } catch (error) {
        console.warn(`⚠️ 检查目录 ${outputDir} 时出错: ${error.message}`);
        return false;
    }
}

// 获取目录下的 JSON 文件数量
function getJsonFileCount(outputDir) {
    try {
        if (!fs.existsSync(outputDir)) {
            return 0;
        }

        const files = fs.readdirSync(outputDir);
        const jsonFiles = files.filter(file => file.match(/^\d+\.json$/));
        
        return jsonFiles.length;
    } catch (error) {
        console.warn(`⚠️ 统计目录 ${outputDir} 中的JSON文件时出错: ${error.message}`);
        return 0;
    }
}

// 检查参数组合的处理状态
function checkParamStatus(combination) {
    const outputDir = generateOutputPath(combination);
    const key = getCombinationKey(combination);
    
    // 检查目录是否存在
    const dirExists = fs.existsSync(outputDir);
    
    // 检查是否有 JSON 文件
    const hasJson = hasJsonFiles(outputDir);
    
    // 获取 JSON 文件数量
    const jsonCount = getJsonFileCount(outputDir);
    
    return {
        combination: combination,
        key: key,
        outputDir: outputDir,
        dirExists: dirExists,
        hasJson: hasJson,
        jsonCount: jsonCount,
        status: hasJson ? 'processed' : 'unprocessed'
    };
}

// 主函数
function main() {
    console.log('🔍 开始检查参数组合处理状态...\n');
    
    // 加载参数组合
    const paramsCombinations = loadParamsCombinations();
    
    if (paramsCombinations.length === 0) {
        console.error('❌ 没有找到参数组合，退出程序');
        return;
    }
    
    // 检查每个参数组合的状态
    const results = paramsCombinations.map(combination => checkParamStatus(combination));
    
    // 分类统计
    const processed = results.filter(result => result.status === 'processed');
    const unprocessed = results.filter(result => result.status === 'unprocessed');
    
    console.log('📊 检查结果统计:');
    console.log(`   总参数组合数: ${results.length}`);
    console.log(`   已处理（有JSON文件）: ${processed.length}`);
    console.log(`   未处理（无JSON文件）: ${unprocessed.length}`);
    console.log(`   处理率: ${((processed.length / results.length) * 100).toFixed(1)}%\n`);
    
    // 显示详细信息
    console.log('📋 详细状态信息:');
    console.log('='.repeat(120));
    console.log('状态'.padEnd(8) + '目录存在'.padEnd(10) + 'JSON数量'.padEnd(10) + '参数组合路径');
    console.log('-'.repeat(120));
    
    results.forEach(result => {
        const status = result.status === 'processed' ? '✅已处理' : '❌未处理';
        const dirExists = result.dirExists ? '✅' : '❌';
        const jsonCount = result.jsonCount.toString().padEnd(8);
        
        console.log(`${status.padEnd(12)} ${dirExists.padEnd(12)} ${jsonCount} ${result.outputDir}`);
    });
    console.log('='.repeat(120));
    
    // 保存已处理的参数组合
    if (processed.length > 0) {
        const processedParams = processed.map(result => result.combination);
        const processedFile = './params-processed.json';
        
        fs.writeFileSync(processedFile, JSON.stringify(processedParams, null, 2), 'utf8');
        console.log(`\n💾 已保存 ${processed.length} 个已处理的参数组合到: ${processedFile}`);
        
        // 显示已处理参数的统计信息
        console.log('\n📈 已处理参数统计:');
        processed.forEach((result, index) => {
            const combo = result.combination;
            console.log(`   ${(index + 1).toString().padStart(3)}. ${combo.studyPhaseName}/${combo.subjectName}/${combo.textbookVersionName}/${combo.ceciName}/${combo.catalogName} (${result.jsonCount} 个JSON文件)`);
        });
    }
    
    // 保存未处理的参数组合
    if (unprocessed.length > 0) {
        const unprocessedParams = unprocessed.map(result => result.combination);
        const unprocessedFile = './params-unprocessed.json';
        
        fs.writeFileSync(unprocessedFile, JSON.stringify(unprocessedParams, null, 2), 'utf8');
        console.log(`\n💾 已保存 ${unprocessed.length} 个未处理的参数组合到: ${unprocessedFile}`);
        
        // 显示未处理参数的信息
        console.log('\n📋 未处理参数列表:');
        unprocessed.forEach((result, index) => {
            const combo = result.combination;
            const reason = !result.dirExists ? '(目录不存在)' : '(目录存在但无JSON文件)';
            console.log(`   ${(index + 1).toString().padStart(3)}. ${combo.studyPhaseName}/${combo.subjectName}/${combo.textbookVersionName}/${combo.ceciName}/${combo.catalogName} ${reason}`);
        });
    }
    
    // 生成检查报告
    const reportFile = './params-check-report.json';
    const report = {
        checkTime: new Date().toISOString(),
        summary: {
            total: results.length,
            processed: processed.length,
            unprocessed: unprocessed.length,
            processedRate: ((processed.length / results.length) * 100).toFixed(1) + '%'
        },
        details: results.map(result => ({
            key: result.key,
            outputDir: result.outputDir,
            dirExists: result.dirExists,
            hasJson: result.hasJson,
            jsonCount: result.jsonCount,
            status: result.status,
            combination: {
                studyPhaseName: result.combination.studyPhaseName,
                subjectName: result.combination.subjectName,
                textbookVersionName: result.combination.textbookVersionName,
                ceciName: result.combination.ceciName,
                catalogName: result.combination.catalogName
            }
        }))
    };
    
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2), 'utf8');
    console.log(`\n📄 已生成详细检查报告: ${reportFile}`);
    
    console.log('\n✅ 参数组合状态检查完成！');
    
    if (unprocessed.length > 0) {
        console.log('\n💡 提示:');
        console.log('   - 可以使用 params-unprocessed.json 来继续爬取未处理的参数组合');
        console.log('   - 可以使用 params-processed.json 来查看已处理的参数组合');
        console.log('   - 查看 params-check-report.json 获取详细的检查报告');
    } else {
        console.log('\n🎉 所有参数组合都已处理完成！');
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    loadParamsCombinations,
    sanitizeDirName,
    generateOutputPath,
    getCombinationKey,
    hasJsonFiles,
    getJsonFileCount,
    checkParamStatus
}; 