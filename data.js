const axios = require('axios')
const fs = require('fs')
const { HttpsProxyAgent } = require('https-proxy-agent');

const PROXY_CONFIG = 'd2196405406:triucylw@171.41.150.177:22376'; // 可通过环境变量设置

// 设置axios全局代理配置
if (PROXY_CONFIG) {
    const proxyUrl = `http://${PROXY_CONFIG}`;
    const agent = new HttpsProxyAgent(proxyUrl);
    
    // 设置全局代理
    axios.defaults.httpsAgent = agent;
    axios.defaults.httpAgent = agent;
    
    // 设置默认配置
    axios.defaults.proxy = false; // 禁用默认代理，使用自定义agent
    
    console.log(`已配置代理: ${PROXY_CONFIG.replace(/:[^:]*@/, ':****@')}`);
    
    // 测试代理连接
    axios.get('https://httpbin.org/ip', { timeout: 10000 })
        .then(response => {
            console.log('代理测试成功，当前IP:', response.data.origin);
        })
        .catch(error => {
            console.log('代理测试失败:', error.message);
        });
}


const studyPhaseSubjectTag = [
    {
        "studyPhaseCode": null,
        "studyPhaseName": null,
        "dictCode": "300",
        "dictName": "高中",
        "childList": [
            {
                "studyPhaseCode": "300",
                "studyPhaseName": "高中",
                "dictCode": "1",
                "dictName": "语文",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "300",
                "studyPhaseName": "高中",
                "dictCode": "2",
                "dictName": "数学",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "300",
                "studyPhaseName": "高中",
                "dictCode": "3",
                "dictName": "英语",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "300",
                "studyPhaseName": "高中",
                "dictCode": "7",
                "dictName": "物理",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "300",
                "studyPhaseName": "高中",
                "dictCode": "8",
                "dictName": "化学",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "300",
                "studyPhaseName": "高中",
                "dictCode": "9",
                "dictName": "生物",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "300",
                "studyPhaseName": "高中",
                "dictCode": "5",
                "dictName": "历史",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "300",
                "studyPhaseName": "高中",
                "dictCode": "6",
                "dictName": "地理",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "300",
                "studyPhaseName": "高中",
                "dictCode": "4",
                "dictName": "政治",
                "childList": null,
                "gray": false
            }
        ],
        "gray": false
    },
    {
        "studyPhaseCode": null,
        "studyPhaseName": null,
        "dictCode": "200",
        "dictName": "初中",
        "childList": [
            {
                "studyPhaseCode": "200",
                "studyPhaseName": "初中",
                "dictCode": "1",
                "dictName": "语文",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "200",
                "studyPhaseName": "初中",
                "dictCode": "2",
                "dictName": "数学",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "200",
                "studyPhaseName": "初中",
                "dictCode": "3",
                "dictName": "英语",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "200",
                "studyPhaseName": "初中",
                "dictCode": "7",
                "dictName": "物理",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "200",
                "studyPhaseName": "初中",
                "dictCode": "8",
                "dictName": "化学",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "200",
                "studyPhaseName": "初中",
                "dictCode": "9",
                "dictName": "生物",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "200",
                "studyPhaseName": "初中",
                "dictCode": "5",
                "dictName": "历史",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "200",
                "studyPhaseName": "初中",
                "dictCode": "6",
                "dictName": "地理",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "200",
                "studyPhaseName": "初中",
                "dictCode": "56",
                "dictName": "道德与法治",
                "childList": null,
                "gray": false
            },
            {
                "studyPhaseCode": "200",
                "studyPhaseName": "初中",
                "dictCode": "57",
                "dictName": "科学",
                "childList": null,
                "gray": false
            }
        ],
        "gray": false
    }
]

const getTextbookVersionCeciTag = async (studyPhaseCode, subjectCode) => {
    const response = await axios.get(`https://qms.stzy.com/matrix/zw-zzw/api/v1/zzw/tag/getTextbookVersionCeciTag/${studyPhaseCode}/${subjectCode}`)
    const data = response.data.data;
    return data;
}

const getCatalogList = async (studyPhaseCode, subjectCode, textbookVersionCode, ceciCode, gradeCode) => {
    const response = await axios.post(`https://qms.stzy.com/matrix/zw-zzw/api/v1/zzw/tree/textbook`, {
        studyPhaseCode,
        subjectCode,
        textbookVersionCode,
        ceciCode,
        gradeCode
    })
    const data = response.data.data[0].children;
    return data;
}

const main = async () => {
    // 等待代理测试完成
    if (PROXY_CONFIG) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('开始执行主要逻辑...');
    }
    
    const json = [];
    for (const item of studyPhaseSubjectTag) {
        const studyPhaseCode = item.dictCode;
        const subjectList = item.childList;
        const studyPhaseName = item.dictName;
        for (const subject of subjectList) {
            const subjectCode = subject.dictCode;
            const subjectName = subject.dictName;
            // 这个第一级是 textbookVersion，第二级是 ceci
            const textbookVersionList = await getTextbookVersionCeciTag(studyPhaseCode, subjectCode);
            for (const textbookVersion of textbookVersionList) {
                const textbookVersionCode = textbookVersion.dictCode;
                const textbookVersionName = textbookVersion.dictName;
                const ceciList = textbookVersion.childList;
                for (const ceci of ceciList) {
                    const ceciCode = ceci.dictCode;
                    const ceciName = ceci.dictName;
                    const gradeCode = ceci.gradeCode;
                    // 还得查一下目录
                    const catalogList = await getCatalogList(studyPhaseCode, subjectCode, textbookVersionCode, ceciCode, gradeCode);
                    for (const catalog of catalogList) {
                        const catalogCode = catalog.id;
                        const catalogName = catalog.title;
                        json.push({
                            studyPhaseCode,
                            studyPhaseName,
                            subjectCode,
                            subjectName,
                            textbookVersionCode,
                            textbookVersionName,
                            ceciCode,
                            ceciName,
                            gradeCode,
                            catalogCode,
                            catalogName,
                        })
                    }

                }
            }
        }
    }
    fs.writeFileSync('params.json', JSON.stringify(json, null, 2));
}
main();