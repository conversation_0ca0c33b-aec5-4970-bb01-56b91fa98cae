const { chromium } = require('playwright');

class BrowserPool {
    constructor(config = {}) {
        this.config = {
            maxBrowsers: config.maxBrowsers || 40, // 最大浏览器实例数
            browserTimeout: config.browserTimeout || 60000, // 浏览器超时时间
            pageTimeout: config.pageTimeout || 30000, // 页面超时时间
            headless: config.headless !== false, // 默认无头模式
            userAgent: config.userAgent || 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
            viewport: config.viewport || { width: 1920, height: 1080 },
            stateFile: config.stateFile || './browser_pool_state.json', // 浏览器池状态文件
            autoSave: config.autoSave !== false, // 自动保存状态
            saveInterval: config.saveInterval || 30000 // 保存间隔
        };

        // 浏览器实例池
        this.browsers = new Map(); // Map<browserId, {browser, proxy, tokenId, isActive, createdAt, lastUsedAt}>
        this.availableBrowsers = new Set(); // 可用的浏览器ID
        this.busyBrowsers = new Set(); // 忙碌的浏览器ID
        
        // 控制标志
        this.isInitialized = false;
        this.isDestroying = false;
        
        // 自动保存定时器
        if (this.config.autoSave) {
            this.autoSaveTimer = setInterval(() => {
                this.saveState();
            }, this.config.saveInterval);
        }
    }

    // 初始化浏览器池
    async initialize() {
        if (this.isInitialized) {
            return;
        }

        console.log(`🌐 正在初始化浏览器池 (最大实例数: ${this.config.maxBrowsers})`);
        
        try {
            // 加载状态
            this.loadState();
            
            this.isInitialized = true;
            console.log(`✅ 浏览器池初始化完成`);
            
        } catch (error) {
            console.error('❌ 浏览器池初始化失败:', error.message);
            throw error;
        }
    }

    // 获取可用的浏览器实例
    async getBrowser(tokenId = null, proxy = null) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // 检查是否有空闲的浏览器
        for (const browserId of this.availableBrowsers) {
            const browserInfo = this.browsers.get(browserId);
            if (browserInfo && browserInfo.isActive) {
                // 分配给指定token（如果提供）
                if (tokenId) {
                    browserInfo.tokenId = tokenId;
                }
                browserInfo.lastUsedAt = Date.now();
                
                // 移动到忙碌列表
                this.availableBrowsers.delete(browserId);
                this.busyBrowsers.add(browserId);
                
                console.log(`🌐 分配浏览器实例[${browserId}] ${tokenId ? `给Token[${tokenId}]` : ''}`);
                return {
                    browserId,
                    browser: browserInfo.browser,
                    proxy: browserInfo.proxy
                };
            }
        }

        // 如果没有可用的浏览器且未达到最大数量，创建新实例
        if (this.browsers.size < this.config.maxBrowsers) {
            return await this.createBrowser(tokenId, proxy);
        }

        // 达到最大数量，返回null
        return null;
    }

    // 创建新的浏览器实例
    async createBrowser(tokenId = null, proxy = null) {
        try {
            // 创建浏览器配置
            const browserConfig = {
                headless: this.config.headless,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--disable-gpu',
                    '--window-size=1920,1080'
                ]
            };

            // 如果有代理，配置代理
            if (proxy) {
                browserConfig.proxy = this.parseProxyConfig(proxy);
            }

            // 启动浏览器
            const browser = await chromium.launch(browserConfig);
            const browserId = this.generateBrowserId();

            // 创建浏览器信息
            const browserInfo = {
                browser,
                proxy,
                tokenId,
                isActive: true,
                createdAt: Date.now(),
                lastUsedAt: Date.now()
            };

            // 添加到池中
            this.browsers.set(browserId, browserInfo);
            this.busyBrowsers.add(browserId);

            // 格式化代理信息用于日志显示
            const proxyInfo = proxy ? this.formatProxyForLog(proxy) : '无代理';
            console.log(`✅ 创建浏览器实例[${browserId}] ${tokenId ? `(Token[${tokenId}])` : ''} (${proxyInfo})`);

            return {
                browserId,
                browser,
                proxy
            };

        } catch (error) {
            console.error('❌ 创建浏览器实例失败:', error.message);
            throw error;
        }
    }

    // 释放浏览器实例
    releaseBrowser(browserId) {
        const browserInfo = this.browsers.get(browserId);
        if (!browserInfo) {
            return;
        }

        // 清除token关联
        browserInfo.tokenId = null;
        browserInfo.lastUsedAt = Date.now();

        // 移动到可用列表
        this.busyBrowsers.delete(browserId);
        this.availableBrowsers.add(browserId);

        console.log(`🔓 释放浏览器实例[${browserId}]`);
    }

    // 关闭指定的浏览器实例
    async closeBrowser(browserId, reason = 'manual') {
        const browserInfo = this.browsers.get(browserId);
        if (!browserInfo) {
            return;
        }

        try {
            // 关闭浏览器
            if (browserInfo.browser) {
                await browserInfo.browser.close();
            }

            // 从池中移除
            this.browsers.delete(browserId);
            this.availableBrowsers.delete(browserId);
            this.busyBrowsers.delete(browserId);

            console.log(`🗑️ 关闭浏览器实例[${browserId}] (原因: ${reason})`);

        } catch (error) {
            console.error(`❌ 关闭浏览器实例[${browserId}]失败:`, error.message);
        }
    }

    // 根据token关闭浏览器
    async closeBrowserByToken(tokenId, reason = 'token_invalid') {
        for (const [browserId, browserInfo] of this.browsers) {
            if (browserInfo.tokenId === tokenId) {
                await this.closeBrowser(browserId, reason);
                break;
            }
        }
    }

    // 创建页面并导航到指定URL
    async createPage(browserId, url = 'https://zj.stzy.com/create-paper/chapter', token = null) {
        const browserInfo = this.browsers.get(browserId);
        if (!browserInfo || !browserInfo.browser) {
            throw new Error(`浏览器实例[${browserId}]不存在或已关闭`);
        }

        try {
            // 创建新的上下文
            const context = await browserInfo.browser.newContext({
                userAgent: this.config.userAgent,
                viewport: this.config.viewport,
                ignoreHTTPSErrors: true
            });

            // 创建页面
            const page = await context.newPage();

            // 设置超时
            page.setDefaultTimeout(this.config.pageTimeout);
            page.setDefaultNavigationTimeout(this.config.pageTimeout);

            // 如果提供了token，设置ACCESS_TOKEN cookie
            if (token) {
                console.log(`🍪 浏览器实例[${browserId}] 设置ACCESS_TOKEN cookie`);
                await context.addCookies([{
                    name: 'ACCESS_TOKEN',
                    value: token,
                    domain: '.stzy.com', // 设置为主域名，确保子域名也能访问
                    path: '/',
                    httpOnly: false,
                    secure: true,
                    sameSite: 'Lax'
                }]);
            }

            // 导航到目标页面
            console.log(`🔗 浏览器实例[${browserId}] 正在导航到: ${url}`);
            await page.goto(url, { 
                waitUntil: 'networkidle', 
                timeout: this.config.pageTimeout 
            });

            console.log(`✅ 浏览器实例[${browserId}] 页面加载完成`);

            return { page, context };

        } catch (error) {
            console.error(`❌ 浏览器实例[${browserId}] 创建页面失败:`, error.message);
            throw error;
        }
    }

    // 清理过期的浏览器实例
    async cleanupExpiredBrowsers() {
        const now = Date.now();
        const expireTime = 10 * 60 * 1000; // 10分钟过期

        for (const [browserId, browserInfo] of this.browsers) {
            // 检查是否过期且不在使用中
            if (now - browserInfo.lastUsedAt > expireTime && 
                this.availableBrowsers.has(browserId)) {
                await this.closeBrowser(browserId, 'expired');
            }
        }
    }

    // 解析代理配置，支持 user:pass@ip:port 格式
    parseProxyConfig(proxy) {
        if (!proxy) return null;

        // 移除协议前缀（如果存在）
        const cleanProxy = proxy.replace(/^https?:\/\//, '');

        // 检查是否包含认证信息
        if (cleanProxy.includes('@')) {
            // 格式: user:pass@ip:port
            const [auth, server] = cleanProxy.split('@');
            const [username, password] = auth.split(':');
            
            return {
                server: `http://${server}`,
                username: username,
                password: password
            };
        } else {
            // 格式: ip:port 或 http://ip:port
            const server = cleanProxy.startsWith('http') ? cleanProxy : `http://${cleanProxy}`;
            return {
                server: server
            };
        }
    }

    // 格式化代理信息用于日志显示（隐藏密码）
    formatProxyForLog(proxy) {
        if (!proxy) return '无代理';

        // 移除协议前缀（如果存在）
        const cleanProxy = proxy.replace(/^https?:\/\//, '');

        // 检查是否包含认证信息
        if (cleanProxy.includes('@')) {
            // 格式: user:pass@ip:port
            const [auth, server] = cleanProxy.split('@');
            const [username] = auth.split(':');
            return `代理: ${username}:***@${server}`;
        } else {
            // 格式: ip:port
            return `代理: ${cleanProxy}`;
        }
    }

    // 生成浏览器ID
    generateBrowserId() {
        return `browser_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // 获取池状态
    getStatus() {
        return {
            total: this.browsers.size,
            available: this.availableBrowsers.size,
            busy: this.busyBrowsers.size,
            maxBrowsers: this.config.maxBrowsers,
            isInitialized: this.isInitialized
        };
    }

    // 打印状态
    printStatus() {
        const status = this.getStatus();
        console.log(`🌐 浏览器池状态:`);
        console.log(`   - 总实例数: ${status.total}/${status.maxBrowsers}`);
        console.log(`   - 可用: ${status.available} 个`);
        console.log(`   - 忙碌: ${status.busy} 个`);
        
        if (this.browsers.size > 0) {
            console.log(`   实例详情:`);
            for (const [browserId, browserInfo] of this.browsers) {
                const status = this.availableBrowsers.has(browserId) ? '可用' : '忙碌';
                const tokenInfo = browserInfo.tokenId ? `Token[${browserInfo.tokenId}]` : '无Token';
                const proxyInfo = browserInfo.proxy ? this.formatProxyForLog(browserInfo.proxy) : '无代理';
                const uptime = Math.floor((Date.now() - browserInfo.createdAt) / 1000);
                console.log(`     • ${browserId}: ${status} - ${tokenInfo} - ${proxyInfo} - 运行 ${uptime}s`);
            }
        }
    }

    // 加载状态
    loadState() {
        // 浏览器池状态不需要持久化，每次启动时重新创建
        console.log(`📋 浏览器池状态已重置`);
    }

    // 保存状态
    saveState() {
        // 浏览器池状态不需要持久化
        // 但可以保存一些统计信息用于调试
        const state = {
            lastSaved: new Date().toISOString(),
            totalBrowsers: this.browsers.size,
            availableBrowsers: this.availableBrowsers.size,
            busyBrowsers: this.busyBrowsers.size
        };

        try {
            const fs = require('fs');
            fs.writeFileSync(this.config.stateFile, JSON.stringify(state, null, 2));
        } catch (error) {
            // 忽略保存错误
        }
    }

    // 销毁浏览器池
    async destroy() {
        if (this.isDestroying) {
            return;
        }

        this.isDestroying = true;
        console.log(`🗑️ 正在销毁浏览器池...`);

        try {
            // 清除自动保存定时器
            if (this.autoSaveTimer) {
                clearInterval(this.autoSaveTimer);
                this.autoSaveTimer = null;
            }

            // 关闭所有浏览器实例
            const closePromises = [];
            for (const browserId of this.browsers.keys()) {
                closePromises.push(this.closeBrowser(browserId, 'pool_destroy'));
            }
            
            await Promise.allSettled(closePromises);

            console.log(`✅ 浏览器池已销毁`);

        } catch (error) {
            console.error('❌ 销毁浏览器池时出错:', error.message);
        }
    }
}

module.exports = BrowserPool; 