#!/usr/bin/env node

/**
 * Enhanced Web Scraping System - Main Entry Point
 * 
 * This is the main entry point for the enhanced web scraping system.
 * It initializes and orchestrates all components for robust, scalable web scraping.
 * 
 * Usage:
 *   node enhanced-main.js [options]
 * 
 * Options:
 *   --config <path>     Configuration file path (default: ./enhanced-config.js)
 *   --environment <env> Environment (development|production) (default: development)
 *   --help              Show help
 *   --version           Show version
 *   --status            Show system status
 *   --health            Show health report
 */

const path = require('path');
const fs = require('fs');
const ScrapingOrchestrator = require('./src/ScrapingOrchestrator');

class EnhancedScrapingSystem {
    constructor() {
        this.orchestrator = null;
        this.args = this.parseArguments();
    }

    /**
     * Parse command line arguments
     */
    parseArguments() {
        const args = {
            config: './enhanced-config.js',
            environment: 'development',
            help: false,
            version: false,
            status: false,
            health: false
        };

        for (let i = 2; i < process.argv.length; i++) {
            const arg = process.argv[i];
            
            switch (arg) {
                case '--config':
                    args.config = process.argv[++i];
                    break;
                case '--environment':
                case '--env':
                    args.environment = process.argv[++i];
                    break;
                case '--help':
                case '-h':
                    args.help = true;
                    break;
                case '--version':
                case '-v':
                    args.version = true;
                    break;
                case '--status':
                    args.status = true;
                    break;
                case '--health':
                    args.health = true;
                    break;
                default:
                    if (arg.startsWith('--')) {
                        console.warn(`Unknown option: ${arg}`);
                    }
                    break;
            }
        }

        return args;
    }

    /**
     * Show help information
     */
    showHelp() {
        console.log(`
Enhanced Web Scraping System

Usage: node enhanced-main.js [options]

Options:
  --config <path>     Configuration file path (default: ./enhanced-config.js)
  --environment <env> Environment (development|production) (default: development)
  --help              Show this help message
  --version           Show version information
  --status            Show current system status
  --health            Show detailed health report

Examples:
  node enhanced-main.js                                    # Start with default settings
  node enhanced-main.js --config ./my-config.js           # Use custom config
  node enhanced-main.js --environment production          # Run in production mode
  node enhanced-main.js --status                          # Show system status
  node enhanced-main.js --health                          # Show health report

Environment Variables:
  NODE_ENV                    Environment (development|production)
  LOG_LEVEL                   Logging level (DEBUG|INFO|WARN|ERROR)
  SCRAPER_HEADLESS           Run browser in headless mode (true|false)
  SCRAPER_MAX_CONCURRENT     Maximum concurrent requests
  TOKEN_VALIDATION_INTERVAL  Token validation interval (ms)
  PARAM_BATCH_SIZE          Parameter batch size
  
For more information, visit: https://github.com/your-repo/enhanced-scraping-system
        `);
    }

    /**
     * Show version information
     */
    showVersion() {
        const packagePath = path.join(__dirname, 'package.json');
        let version = '2.0.0';
        
        try {
            if (fs.existsSync(packagePath)) {
                const packageInfo = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
                version = packageInfo.version || version;
            }
        } catch (error) {
            // Use default version
        }

        console.log(`Enhanced Web Scraping System v${version}`);
        console.log(`Node.js ${process.version}`);
        console.log(`Platform: ${process.platform} ${process.arch}`);
    }

    /**
     * Validate configuration file
     */
    validateConfig() {
        if (!fs.existsSync(this.args.config)) {
            throw new Error(`Configuration file not found: ${this.args.config}`);
        }

        try {
            const config = require(path.resolve(this.args.config));
            
            // Basic validation
            if (!config.files) {
                throw new Error('Configuration missing required "files" section');
            }
            
            if (!config.scraping) {
                throw new Error('Configuration missing required "scraping" section');
            }

            return config;
        } catch (error) {
            throw new Error(`Invalid configuration file: ${error.message}`);
        }
    }

    /**
     * Show system status
     */
    async showStatus() {
        try {
            console.log('🔍 Checking system status...\n');

            // Initialize orchestrator for status check
            this.orchestrator = new ScrapingOrchestrator(this.args.config, this.args.environment);
            await this.orchestrator.initialize();

            // Wait a moment for components to stabilize
            await new Promise(resolve => setTimeout(resolve, 2000));

            const status = this.orchestrator.getStatus();

            console.log('📊 System Status Report');
            console.log('========================\n');

            // Orchestrator status
            console.log('🎯 Orchestrator:');
            console.log(`   Initialized: ${status.orchestrator.isInitialized ? '✅' : '❌'}`);
            console.log(`   Running: ${status.orchestrator.isRunning ? '✅' : '❌'}`);
            console.log(`   Total Processed: ${status.orchestrator.processingStats.totalProcessed}`);
            console.log(`   Success Count: ${status.orchestrator.processingStats.successCount}`);
            console.log(`   Error Count: ${status.orchestrator.processingStats.errorCount}\n`);

            // Parameters status
            if (status.parameters) {
                console.log('📋 Parameters:');
                console.log(`   Total: ${status.parameters.total}`);
                console.log(`   Completed: ${status.parameters.completed} (${status.parameters.completionRate.toFixed(1)}%)`);
                console.log(`   Pending: ${status.parameters.pending}`);
                console.log(`   Processing: ${status.parameters.processing}`);
                console.log(`   Failed: ${status.parameters.failed}\n`);
            }

            // Tokens status
            if (status.tokens) {
                console.log('🔐 Tokens:');
                console.log(`   Total: ${status.tokens.totalTokens}`);
                console.log(`   Valid: ${status.tokens.validTokens}`);
                console.log(`   Healthy: ${status.tokens.healthyTokens}`);
                console.log(`   Unhealthy: ${status.tokens.unhealthyTokens}\n`);
            }

            // Crawler status
            if (status.crawler) {
                console.log('🕷️ Crawler:');
                console.log(`   Initialized: ${status.crawler.isInitialized ? '✅' : '❌'}`);
                console.log(`   Success Rate: ${status.crawler.successRate.toFixed(1)}%`);
                console.log(`   Average Response Time: ${Math.round(status.crawler.averageResponseTime)}ms`);
                console.log(`   Active Pages: ${status.crawler.activePages}`);
                console.log(`   Active Contexts: ${status.crawler.activeContexts}\n`);
            }

            // Health status
            if (status.health) {
                console.log('🏥 Health:');
                console.log(`   Overall: ${this.getHealthEmoji(status.health.overallHealth)} ${status.health.overallHealth}`);
                console.log(`   Recent Alerts: ${status.health.recentAlerts.length}`);
                
                if (status.health.componentHealth) {
                    console.log('   Components:');
                    for (const [component, health] of Object.entries(status.health.componentHealth)) {
                        console.log(`     ${component}: ${this.getHealthEmoji(health.status)} ${health.status}`);
                    }
                }
                console.log();
            }

            await this.orchestrator.shutdown();

        } catch (error) {
            console.error('❌ Failed to get system status:', error.message);
            process.exit(1);
        }
    }

    /**
     * Show detailed health report
     */
    async showHealth() {
        try {
            console.log('🏥 Generating health report...\n');

            // Initialize orchestrator for health check
            this.orchestrator = new ScrapingOrchestrator(this.args.config, this.args.environment);
            await this.orchestrator.initialize();

            // Wait a moment for health data to be collected
            await new Promise(resolve => setTimeout(resolve, 5000));

            const healthReport = this.orchestrator.healthMonitor.generateHealthReport();

            console.log('🏥 Detailed Health Report');
            console.log('==========================\n');

            console.log(`Generated: ${new Date(healthReport.generatedAt).toLocaleString()}`);
            console.log(`Instance: ${healthReport.instanceId}\n`);

            // Summary
            console.log('📊 Summary:');
            console.log(`   Overall Health: ${this.getHealthEmoji(healthReport.summary.overallHealth)} ${healthReport.summary.overallHealth}`);
            console.log(`   Last Check: ${new Date(healthReport.summary.lastHealthCheck).toLocaleString()}`);
            console.log(`   Total Alerts (24h): ${healthReport.summary.totalAlerts}`);
            console.log(`   Critical Alerts (24h): ${healthReport.summary.criticalAlerts}\n`);

            // Health trend
            console.log(`📈 Health Trend: ${this.getTrendEmoji(healthReport.healthTrend)} ${healthReport.healthTrend}\n`);

            // Recent alerts
            if (healthReport.alerts.length > 0) {
                console.log('🚨 Recent Alerts:');
                healthReport.alerts.slice(-5).forEach(alert => {
                    console.log(`   ${this.getSeverityEmoji(alert.severity)} ${alert.message}`);
                    console.log(`     Time: ${new Date(alert.timestamp).toLocaleString()}`);
                });
                console.log();
            }

            // Recommendations
            if (healthReport.recommendations.length > 0) {
                console.log('💡 Recommendations:');
                healthReport.recommendations.forEach(rec => {
                    console.log(`   ${this.getPriorityEmoji(rec.priority)} ${rec.message}`);
                });
                console.log();
            }

            await this.orchestrator.shutdown();

        } catch (error) {
            console.error('❌ Failed to generate health report:', error.message);
            process.exit(1);
        }
    }

    /**
     * Get health status emoji
     */
    getHealthEmoji(status) {
        switch (status) {
            case 'healthy': return '✅';
            case 'warning': return '⚠️';
            case 'degraded': return '🟡';
            case 'critical': return '❌';
            default: return '❓';
        }
    }

    /**
     * Get trend emoji
     */
    getTrendEmoji(trend) {
        switch (trend) {
            case 'improving': return '📈';
            case 'stable': return '➡️';
            case 'degrading': return '📉';
            default: return '❓';
        }
    }

    /**
     * Get severity emoji
     */
    getSeverityEmoji(severity) {
        switch (severity) {
            case 'critical': return '🔴';
            case 'warning': return '🟡';
            case 'info': return '🔵';
            default: return '⚪';
        }
    }

    /**
     * Get priority emoji
     */
    getPriorityEmoji(priority) {
        switch (priority) {
            case 'critical': return '🔴';
            case 'high': return '🟠';
            case 'medium': return '🟡';
            case 'low': return '🟢';
            default: return '⚪';
        }
    }

    /**
     * Main execution function
     */
    async run() {
        try {
            // Handle special commands
            if (this.args.help) {
                this.showHelp();
                return;
            }

            if (this.args.version) {
                this.showVersion();
                return;
            }

            if (this.args.status) {
                await this.showStatus();
                return;
            }

            if (this.args.health) {
                await this.showHealth();
                return;
            }

            // Validate configuration
            this.validateConfig();

            console.log('🚀 Starting Enhanced Web Scraping System...');
            console.log(`📁 Config: ${this.args.config}`);
            console.log(`🌍 Environment: ${this.args.environment}\n`);

            // Initialize and start orchestrator
            this.orchestrator = new ScrapingOrchestrator(this.args.config, this.args.environment);
            await this.orchestrator.initialize();
            await this.orchestrator.start();

        } catch (error) {
            console.error('💥 Fatal error:', error.message);
            
            if (this.orchestrator) {
                await this.orchestrator.shutdown();
            }
            
            process.exit(1);
        }
    }
}

// Run the application
if (require.main === module) {
    const system = new EnhancedScrapingSystem();
    system.run().catch(error => {
        console.error('💥 Unhandled error:', error);
        process.exit(1);
    });
}

module.exports = EnhancedScrapingSystem;
