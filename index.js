const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { HttpsProxyAgent } = require('https-proxy-agent');
const BrowserPool = require('./BrowserPool');
const ProxyPool = require('./ProxyPool');

// 尝试加载配置文件
let defaultConfig = {};
try {
    defaultConfig = require('./config');
    console.log('✅ 已加载配置文件: config.js');
} catch (error) {
    console.log('⚠️ 未找到配置文件 config.js，将使用默认配置');
}

// 加载参数组合文件
let paramsCombinations = [];
try {
    paramsCombinations = require('./params.json');
    console.log(`✅ 已加载参数组合文件: ${paramsCombinations.length} 个组合`);
} catch (error) {
    console.log('⚠️ 未找到参数组合文件 params.json');
}

class DataCrawler {
    constructor(config = {}) {
        // 合并默认配置和传入配置
        const mergedConfig = { ...defaultConfig, ...config };
        
        // 配置参数
        this.config = {
            // 基础配置
            baseURL: 'https://qms.stzy.com/matrix/zw-search/api/v1/homeEs/question/textbookQuery',
            tokensFile: mergedConfig.tokensFile || './tokens.json', // tokens文件路径
            
            // 浏览器池配置
            maxBrowsers: mergedConfig.maxBrowsers || 5, // 最大浏览器实例数
            browserTimeout: mergedConfig.browserTimeout || 60000, // 浏览器超时
            pageTimeout: mergedConfig.pageTimeout || 30000, // 页面超时
            headless: mergedConfig.headless !== false, // 默认无头模式
            
            // 代理配置（可选）
            proxy: mergedConfig.proxy || null, // 例如: { host: '127.0.0.1', port: 8080 }
            proxyUrl: mergedConfig.proxyUrl || null, // 获取代理的URL
            
            // 并发配置
            concurrency: mergedConfig.concurrency || 3, // 每个token的并发数量
            maxConcurrentTokens: mergedConfig.maxConcurrentTokens || null, // 同时使用的token数量，null表示使用所有可用token
            
            // 等待时间配置（毫秒）
            minDelay: mergedConfig.minDelay || 3000,
            maxDelay: mergedConfig.maxDelay || 30500,
            
            // 重试配置
            maxRetries: mergedConfig.maxRetries || 1, // 单个请求最大重试次数
            retryDelay: mergedConfig.retryDelay || 5000, // 重试间隔（毫秒）
            
            // 分页配置
            startPage: mergedConfig.startPage || 1,
            pageSize: mergedConfig.pageSize || 20,
            maxPages: mergedConfig.maxPages || null, // null表示无限制
            
            // Token 验证配置
            enableTokenValidation: mergedConfig.enableTokenValidation || false,
            tokenValidationInterval: mergedConfig.tokenValidationInterval || 60,
            
            // 参数组合配置
            paramsCombinations: paramsCombinations, // 参数组合数组
            currentCombinationIndex: mergedConfig.currentCombinationIndex || 0, // 当前参数组合索引
        };
        
        // 初始化代理池
        this.initProxyPool();
        
        // 初始化参数组合相关
        this.initParamsCombinations();
        
        // 创建输出目录
        this.ensureOutputDir();
        
        // 加载tokens
        this.loadTokens();
        
        // 加载或初始化状态
        this.loadState();
        
        // 初始化浏览器池
        this.setupBrowserPool();
        
        // 初始化并发管理器
        this.initConcurrencyManager();
    }
    
    // 初始化代理池
    initProxyPool() {
        if (this.config.proxyUrl) {
            this.proxyPool = new ProxyPool({
                proxyUrl: this.config.proxyUrl,
                proxyValidityDuration: 8, // 8分钟有效期
                cleanupInterval: 60000,
                stateFile: './proxy_pool_state.json',
                autoSave: true,
                saveInterval: 30000
            });
            console.log('🏊 代理池已启用');
        } else {
            this.proxyPool = null;
            console.log('⚠️ 未配置代理获取URL，代理功能未启用');
        }
    }
    
    // 为token分配代理
    async assignProxyToToken(tokenData) {
        // 如果有静态代理配置，优先使用
        if (this.config.proxy) {
            const proxyUrl = `http://${this.config.proxy.host}:${this.config.proxy.port}`;
            console.log(`🔗 为Token[${tokenData.id}]使用静态代理: ${proxyUrl}`);
            return proxyUrl;
        }
        
        // 如果有代理池，获取代理
        if (this.proxyPool) {
            try {
                const proxyUrl = await this.proxyPool.assignProxyToToken(tokenData.id);
                if (proxyUrl) {
                    console.log(`🔗 为Token[${tokenData.id}]分配代理: ${proxyUrl}`);
                    return proxyUrl;
                } else {
                    console.log(`⚠️ 无法为Token[${tokenData.id}]获取代理，将使用无代理模式`);
                    return null;
                }
            } catch (error) {
                console.error(`❌ 为Token[${tokenData.id}]分配代理失败: ${error.message}`);
                return null;
            }
        }
        
        // 无代理配置
        console.log(`📝 Token[${tokenData.id}]使用无代理模式`);
        return null;
    }
    
    // 释放token的代理
    releaseProxyFromToken(tokenId) {
        if (this.proxyPool) {
            this.proxyPool.releaseProxyFromToken(tokenId);
        }
    }
    
    // 初始化并发管理器
    initConcurrencyManager() {
        // 任务队列
        this.taskQueue = {
            pending: new Set(), // 等待处理的页面
            processing: new Map(), // 正在处理的页面 Map<pageNum, {tokenId, startTime, promise}>
            completed: new Set(), // 已完成的页面
            failed: new Set(), // 失败的页面
        };
        
        // Token池管理
        this.tokenPool = {
            available: [], // 可用的token
            busy: new Map(), // 忙碌的token Map<tokenId, {activeRequests, maxRequests}>
            invalid: new Set(), // 无效的token
            banned: new Map(), // 被封禁的token Map<tokenId, unbanTime>
        };
        
        // 工作线程池
        this.workerPool = {
            workers: new Map(), // 活跃的工作线程 Map<workerId, {tokenId, pageNum, promise}>
            maxWorkers: this.getMaxWorkers(), // 最大工作线程数
            currentWorkers: 0, // 当前工作线程数
        };
        
        // 控制标志
        this.isRunning = false;
        this.isPaused = false;
    }
    
    // 计算最大工作线程数
    getMaxWorkers() {
        const maxTokens = this.config.maxConcurrentTokens || this.tokenData.length;
        return maxTokens * this.config.concurrency;
    }
    
    // 初始化任务队列
    initTaskQueue() {
        // 清空队列
        this.taskQueue.pending.clear();
        this.taskQueue.completed = new Set(this.completedPages);
        this.taskQueue.failed = new Set(this.failedPages);
        
        // 添加失败页面到队列（优先处理）
        this.failedPages.forEach(page => {
            this.taskQueue.pending.add(page);
        });
        
        // 添加新页面到队列
        let pageNum = this.currentPage;
        const maxPages = this.config.maxPages;
        
        // 预先准备一些页面到队列中
        const initialQueueSize = Math.min(100, this.workerPool.maxWorkers * 2); // 预先准备2倍工作线程数的页面
        for (let i = 0; i < initialQueueSize && (maxPages === null || pageNum <= maxPages); i++) {
            if (!this.taskQueue.completed.has(pageNum) && !this.taskQueue.failed.has(pageNum)) {
                this.taskQueue.pending.add(pageNum);
            }
            pageNum++;
        }
        
        console.log(`📋 任务队列初始化: 待处理 ${this.taskQueue.pending.size} 页, 已完成 ${this.taskQueue.completed.size} 页, 失败 ${this.taskQueue.failed.size} 页`);
    }
    
    // 初始化Token池
    initTokenPool() {
        this.tokenPool.available = [];
        this.tokenPool.busy.clear();
        this.tokenPool.invalid.clear();
        this.tokenPool.banned.clear();
        
        // 检查所有token状态
        this.tokenData.forEach(tokenData => {
            if (tokenData.isValid === false) {
                this.tokenPool.invalid.add(tokenData.id);
            } else if (tokenData.isValid === 'banned') {
                // 被封禁的token直接加入封禁池，等待API检查
                this.tokenPool.banned.set(tokenData.id, null);
            } else {
                this.tokenPool.available.push(tokenData);
            }
        });
    }
    
    // 获取下一个待处理的页面
    getNextTask() {
        // 优先处理失败页面
        for (const page of this.taskQueue.pending) {
            if (this.taskQueue.failed.has(page)) {
                this.taskQueue.pending.delete(page);
                return page;
            }
        }
        
        // 处理普通页面
        for (const page of this.taskQueue.pending) {
            this.taskQueue.pending.delete(page);
            return page;
        }
        
        // 如果队列为空，尝试添加新页面
        const maxPages = this.config.maxPages;
        let pageNum = this.currentPage;
        
        while (maxPages === null || pageNum <= maxPages) {
            if (!this.taskQueue.completed.has(pageNum) && 
                !this.taskQueue.processing.has(pageNum) && 
                !this.taskQueue.failed.has(pageNum)) {
                this.currentPage = pageNum + 1;
                return pageNum;
            }
            pageNum++;
        }
        
        return null; // 没有更多任务
    }
    
    // 获取可用的token
    async getAvailableToken() {
        // 检查是否有解封的token
        await this.checkBannedTokens();
        
        // 优先使用完全空闲的token
        for (const tokenData of this.tokenPool.available) {
            if (!this.tokenPool.busy.has(tokenData.id)) {
                return tokenData;
            }
        }
        
        // 查找未达到并发限制的token
        for (const tokenData of this.tokenPool.available) {
            const busyInfo = this.tokenPool.busy.get(tokenData.id);
            if (busyInfo && busyInfo.activeRequests < this.config.concurrency) {
                return tokenData;
            }
        }
        
        return null;
    }
    
    // 检查被封禁的token是否可以解封（通过API检查）
    async checkBannedTokens() {
        const now = Date.now();
        const checkInterval = 60 * 1000; // 1分钟检查间隔
        
        for (const [tokenId] of this.tokenPool.banned) {
            const tokenData = this.tokenData.find(t => t.id === tokenId);
            if (!tokenData) continue;
            
            // 检查是否需要进行状态检查（每分钟检查一次）
            const lastCheck = tokenData.lastBanCheck ? new Date(tokenData.lastBanCheck).getTime() : 0;
            if (now - lastCheck < checkInterval) {
                continue; // 还未到检查时间
            }
            
            // 更新检查时间
            tokenData.lastBanCheck = new Date().toISOString();
            
            try {
                const result = await this.validateToken(tokenData, 'check_ban');
                if (result.valid && !result.banned) {
                    // token已解封
                    this.unbanToken(tokenData);
                }
            } catch (error) {
                console.error(`❌ 检查Token[${tokenId}]封禁状态失败:`, error.message);
            }
        }
    }
    
    // 验证token状态（合并了performTokenValidation和checkTokenBanStatus的功能）
    async validateToken(tokenData, mode = 'validate') {
        try {
            const modeText = mode === 'check_ban' ? '检查封禁状态' : '验证有效性';
            console.log(`🔍 正在${modeText} Token[${tokenData.id}]...`);
            
            // 获取代理配置
            const proxyUrl = await this.assignProxyToToken(tokenData);
            
            // 创建axios配置
            const axiosConfig = {
                timeout: 10000,
                headers: {
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,is;q=0.8,en;q=0.7',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Origin': 'https://zj.stzy.com',
                    'Pragma': 'no-cache',
                    'Referer': 'https://zj.stzy.com/',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-site',
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
                    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"macOS"',
                    'token': tokenData.token
                }
            };
            
            // 如果有代理，使用httpsAgent
            if (proxyUrl) {
                axiosConfig.httpsAgent = new HttpsProxyAgent(proxyUrl);
            }
            
            const testAxios = axios.create(axiosConfig);
            
            // 根据模式选择不同的验证方式
            let response;
            if (mode === 'check_ban') {
                // 检查封禁状态模式：使用GET请求
                response = await testAxios.get('https://qms.stzy.com/matrix/zw-zzw/api/v1/zzw/user/detail');
            } else {
                // 验证模式：使用POST请求
                const testData = {
                    pageNum: 1,
                    pageSize: 1,
                    params: this.getCurrentParams()
                };
                response = await testAxios.post('https://qms.stzy.com/matrix/zw-zzw/api/v1/zzw/user/detail', testData);
            }
            
            // 检查响应状态和数据
            if (response.data && response.data.code === 403) {
                if (mode === 'check_ban') {
                    console.log(`🚫 Token[${tokenData.id}] 仍处于封禁状态`);
                    return { valid: false, banned: true };
                } else {
                    await this.markTokenBanned(tokenData, '响应code=403，可能被暂时限制');
                    return { valid: false, banned: true };
                }
            }
            
            // 验证成功
            if (mode === 'validate') {
                tokenData.isValid = true;
                tokenData.lastValidated = new Date().toISOString();
                tokenData.bannedUntil = null;
                this.saveTokens();
            }
            
            console.log(`✅ Token[${tokenData.id}] ${modeText}成功`);
            return { valid: true, banned: false };
            
        } catch (error) {
            // 处理错误
            if (error.response && error.response.status === 403) {
                if (mode === 'check_ban') {
                    console.log(`🚫 Token[${tokenData.id}] 仍处于封禁状态`);
                    return { valid: false, banned: true };
                } else {
                    await this.markTokenBanned(tokenData, `验证时遇到403错误，可能被暂时限制`);
                    return { valid: false, banned: true };
                }
            } else if (error.response && error.response.status === 401) {
                // 401错误表示token无效
                if (mode === 'validate') {
                    await this.markTokenInvalid(tokenData, `认证错误 (状态码: 401)`);
                }
                console.error(`❌ Token[${tokenData.id}] 认证失败，token可能已过期`);
                return { valid: false, banned: false };
            } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
                // 网络错误，保持原状态
                console.error(`❌ Token[${tokenData.id}] 验证失败: 网络连接错误`);
                return { valid: false, banned: false, networkError: true };
            } else {
                // 其他错误
                if (mode === 'check_ban') {
                    console.log(`⚠️ Token[${tokenData.id}] 检查时遇到其他错误: ${error.message}`);
                    return { valid: false, banned: true }; // 保守处理
                } else {
                    await this.markTokenBanned(tokenData, `验证时遇到未知错误: ${error.message}`);
                    return { valid: false, banned: true };
                }
            }
        }
    }
    
    // 解封token
    unbanToken(tokenData) {
        console.log(`🔓 Token[${tokenData.id}] 已解封，重新加入可用池`);
        
        // 从封禁池中移除
        this.tokenPool.banned.delete(tokenData.id);
        
        // 重置token状态
        tokenData.isValid = null; // 重置为未验证状态，将在下次使用时重新验证
        tokenData.bannedUntil = null;
        tokenData.lastBanCheck = null;
        tokenData.lastValidated = null;
        
        // 重新加入可用池
        this.tokenPool.available.push(tokenData);
        
        // 保存token状态
        this.saveTokens();
    }
    
    // 占用token
    occupyToken(tokenData) {
        if (!this.tokenPool.busy.has(tokenData.id)) {
            this.tokenPool.busy.set(tokenData.id, {
                activeRequests: 1,
                maxRequests: this.config.concurrency
            });
        } else {
            this.tokenPool.busy.get(tokenData.id).activeRequests++;
        }
    }
    
    // 释放token
    releaseToken(tokenId) {
        const busyInfo = this.tokenPool.busy.get(tokenId);
        if (busyInfo) {
            busyInfo.activeRequests--;
            if (busyInfo.activeRequests <= 0) {
                this.tokenPool.busy.delete(tokenId);
            }
        }
    }
    
    // 标记token为无效
    async markTokenInvalid(tokenData, reason = 'unknown') {
        console.log(`🚫 标记Token[${tokenData.id}]为永久无效 (原因: ${reason})`);
        tokenData.isValid = false;
        tokenData.lastValidated = new Date().toISOString();
        tokenData.bannedUntil = null;
        
        // 从可用池中移除
        const index = this.tokenPool.available.findIndex(t => t.id === tokenData.id);
        if (index !== -1) {
            this.tokenPool.available.splice(index, 1);
        }
        
        // 添加到无效池
        this.tokenPool.invalid.add(tokenData.id);
        
        // 释放token占用
        this.tokenPool.busy.delete(tokenData.id);
        
        // 释放代理
        this.releaseProxyFromToken(tokenData.id);
        
        // 关闭相关的浏览器实例
        if (this.browserPool) {
            await this.browserPool.closeBrowserByToken(`token_${tokenData.id}`, 'token_invalid');
        }
        
        this.saveTokens();
    }
    
    // 标记token为暂时封禁
    async markTokenBanned(tokenData, reason = '请求格式错误或被暂时封禁') {
        console.log(`🚫 Token[${tokenData.id}] ${reason}，已加入封禁检查队列`);
        
        tokenData.isValid = 'banned';
        tokenData.lastValidated = new Date().toISOString();
        tokenData.bannedUntil = null; // 不再基于时间解封
        tokenData.lastBanCheck = null; // 上次检查封禁状态的时间
        
        // 从可用池中移除
        const index = this.tokenPool.available.findIndex(t => t.id === tokenData.id);
        if (index !== -1) {
            this.tokenPool.available.splice(index, 1);
        }
        
        // 添加到封禁池（不设置具体时间）
        this.tokenPool.banned.set(tokenData.id, null);
        
        // 释放token占用
        this.tokenPool.busy.delete(tokenData.id);
        
        // 释放代理
        this.releaseProxyFromToken(tokenData.id);
        
        // 关闭相关的浏览器实例
        if (this.browserPool) {
            await this.browserPool.closeBrowserByToken(`token_${tokenData.id}`, 'token_banned');
        }
        
        this.saveTokens();
    }
    
    // 创建工作线程
    async createWorker() {
        if (this.workerPool.currentWorkers >= this.workerPool.maxWorkers) {
            return false; // 已达到最大工作线程数
        }
        
        const pageNum = this.getNextTask();
        if (pageNum === null) {
            return false; // 没有更多任务
        }
        
        const tokenData = await this.getAvailableToken();
        if (!tokenData) {
            // 将页面放回队列
            this.taskQueue.pending.add(pageNum);
            return false; // 没有可用token
        }
        
        // 验证token
        if (this.config.enableTokenValidation) {
            const isValid = await this.validateTokenData(tokenData);
            if (!isValid) {
                // token验证失败，将页面放回队列
                this.taskQueue.pending.add(pageNum);
                return false;
            }
        }
        
        // 占用token
        this.occupyToken(tokenData);
        
        // 创建工作线程ID
        const workerId = `worker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 标记页面为处理中
        const workerPromise = this.processPage(pageNum, tokenData, workerId);
        this.taskQueue.processing.set(pageNum, {
            tokenId: tokenData.id,
            startTime: Date.now(),
            promise: workerPromise
        });
        
        // 添加到工作线程池
        this.workerPool.workers.set(workerId, {
            tokenId: tokenData.id,
            pageNum: pageNum,
            promise: workerPromise
        });
        
        this.workerPool.currentWorkers++;
        
        // 处理工作线程完成
        workerPromise.finally(() => {
            // 清理工作线程
            this.workerPool.workers.delete(workerId);
            this.workerPool.currentWorkers--;
            
            // 清理处理中的页面
            this.taskQueue.processing.delete(pageNum);
            
            // 释放token
            this.releaseToken(tokenData.id);
        });
        
        return true; // 成功创建工作线程
    }
    
    // 处理单个页面（工作线程的核心逻辑）
    async processPage(pageNum, tokenData, workerId) {
        try {
            const result = await this.fetchPageWithToken(pageNum, tokenData);
            
            if (result.success) {
                // 标记为已完成
                this.taskQueue.completed.add(pageNum);
                this.taskQueue.failed.delete(pageNum); // 确保从失败列表中移除
                this.completedPages.add(pageNum);
                this.failedPages.delete(pageNum);
                
                // 检查是否当前参数组合已完成
                if (result.combinationCompleted) {
                    console.log(`🔄 检测到参数组合完成信号，准备切换到下一个参数组合...`);
                    await this.checkAndSwitchCombination();
                } else if (!result.hasData && !result.skipped) {
                    await this.checkAndSwitchCombination();
                }
                
                // 随机延迟
                if (result.hasData) {
                    await this.randomDelay();
                }
            } else {
                // 标记为失败
                this.taskQueue.failed.add(pageNum);
                this.failedPages.add(pageNum);
            }
            
            // 保存状态
            this.saveState();
            
            return result;
            
        } catch (error) {
            console.error(`❌ Node.js请求第 ${pageNum} 页时出错:`, error.message);
            
            // 标记为失败
            this.taskQueue.failed.add(pageNum);
            this.failedPages.add(pageNum);
            this.saveState();
            
            return { success: false, hasData: false, count: 0, error: error.message };
        }
    }
    
    // 检查并切换参数组合
    async checkAndSwitchCombination() {
        console.log(`🔍 检查参数组合切换条件 - 失败页面: ${this.taskQueue.failed.size}, 等待页面: ${this.taskQueue.pending.size}, 处理中页面: ${this.taskQueue.processing.size}`);
        
        // 等待所有正在处理的任务完成，避免在切换时丢失任务
        if (this.taskQueue.processing.size > 0) {
            console.log(`⏳ 等待 ${this.taskQueue.processing.size} 个正在处理的页面完成...`);
            const processingPromises = Array.from(this.taskQueue.processing.values()).map(task => task.promise);
            await Promise.allSettled(processingPromises);
        }
        
        // 如果当前组合已经没有新数据且没有失败页面，切换到下一个
        if (this.taskQueue.failed.size === 0) {
            const hasSwitched = await this.switchToNextCombination();
            if (!hasSwitched) {
                console.log('🏁 所有参数组合已处理完成，停止运行');
                this.isRunning = false;
            } else {
                // 重新初始化任务队列
                console.log('📋 重新初始化任务队列...');
                this.initTaskQueue();
            }
        } else {
            console.log(`⚠️ 当前组合仍有 ${this.taskQueue.failed.size} 个失败页面，暂停运行等待重试`);
            this.isRunning = false;
        }
    }
    
    // 主并发控制器（简化版）
    async processConcurrentPages() {
        // 初始化管理器
        this.initTaskQueue();
        this.initTokenPool();
        
        this.isRunning = true;
        let noTaskWaitCount = 0;
        let noTokenWaitCount = 0;
        
        while (this.isRunning && !this.isPaused) {
            // 尝试创建新的工作线程
            const created = await this.createWorker();
            
            if (created) {
                // 成功创建工作线程，重置等待计数
                noTaskWaitCount = 0;
                noTokenWaitCount = 0;
                
                // 短暂等待避免过快循环
                await new Promise(resolve => setTimeout(resolve, 100));
            } else {
                // 无法创建工作线程，检查原因
                if (this.taskQueue.pending.size === 0 && this.getNextTask() === null) {
                    // 没有更多任务
                    if (this.workerPool.currentWorkers === 0) {
                        break;
                    } else {
                        noTaskWaitCount++;
                        if (noTaskWaitCount === 1) {
                            console.log(`⏳ 等待 ${this.workerPool.currentWorkers} 个工作线程完成...`);
                        }
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                } else if (await this.getAvailableToken() === null) {
                    // 没有可用token
                    noTokenWaitCount++;
                    if (noTokenWaitCount === 1) {
                        console.log(`⏳ 等待可用token...`);
                    }
                    
                    // 检查是否有token可以解封
                    await this.checkBannedTokens();
                    
                    await new Promise(resolve => setTimeout(resolve, 5000));
                } else {
                    // 已达到最大工作线程数
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
        }
        
        // 等待所有工作线程完成
        if (this.workerPool.currentWorkers > 0) {
            const workerPromises = Array.from(this.workerPool.workers.values()).map(worker => worker.promise);
            await Promise.allSettled(workerPromises);
        }
    }
    
    // 初始化浏览器池
    setupBrowserPool() {
        // 确保浏览器数量至少等于token数量，以支持每个token一个浏览器实例
        const minBrowsers = this.tokenData.length || 1;
        const maxBrowsers = Math.max(this.config.maxBrowsers, minBrowsers);
        
        this.browserPool = new BrowserPool({
            maxBrowsers: maxBrowsers,
            browserTimeout: this.config.browserTimeout,
            pageTimeout: this.config.pageTimeout,
            headless: this.config.headless,
            stateFile: path.join(this.outputDir, 'browser_pool_state.json'),
            autoSave: true,
            saveInterval: 30000
        });
        
        console.log(`🌐 浏览器池配置: 最大浏览器数 ${maxBrowsers} (tokens: ${minBrowsers})`);
    }
    
    // 初始化参数组合
    initParamsCombinations() {
        if (this.config.paramsCombinations.length === 0) {
            console.error('❌ 没有找到有效的参数组合');
            return;
        }
        
        // 设置当前参数组合
        this.currentCombination = this.config.paramsCombinations[this.config.currentCombinationIndex] || this.config.paramsCombinations[0];
        
        // 构建目录路径: ./studyPhaseName/subjectName/textbookVersionName/ceciName
        this.outputDir = path.join(
            './',
            this.sanitizeDirName(this.currentCombination.studyPhaseName),
            this.sanitizeDirName(this.currentCombination.subjectName),
            this.sanitizeDirName(this.currentCombination.textbookVersionName),
            this.sanitizeDirName(this.currentCombination.ceciName)
        );
        
        // 状态文件在当前输出目录下
        this.stateFile = path.join(this.outputDir, 'crawler_state.json');
        
        // 初始化其他变量
        this.currentPage = this.config.startPage;
        this.results = [];
        this.completedPages = new Set();
        this.failedPages = new Set(); // 记录失败的页面
        this.lastError = null;
        
        // Token 管理相关
        this.tokenData = []; // 完整的token数据对象数组
        this.tokenValidationPromise = new Map(); // 每个token的验证Promise
        
        // 浏览器池管理
        this.browserPool = null; // 将在setupBrowserPool()中初始化
        
        console.log(`📂 参数组合 [${this.config.currentCombinationIndex + 1}/${this.config.paramsCombinations.length}]: ${this.currentCombination.studyPhaseName}/${this.currentCombination.subjectName}/${this.currentCombination.textbookVersionName}/${this.currentCombination.ceciName}`);
        console.log(`📁 输出目录: ${this.outputDir}`);
    }
    
    // 清理文件/目录名称中的特殊字符
    sanitizeDirName(name) {
        if (!name) return 'unknown';
        return name.replace(/[<>:"/\\|?*]/g, '_').trim();
    }
    
    // 获取当前请求参数
    getCurrentParams() {
        // 从配置文件获取基础参数
        const baseParams = defaultConfig.params || {
            "searchType": 1,
            "sort": 0,
            "yearCode": "",
            "gradeCode": "",
            "provinceCode": "",
            "cityCode": "",
            "areaCode": "",
            "organizationCode": "",
            "termCode": "",
            "keyWord": "",
            "filterQuestionFlag": false,
            "searchScope": 0,
            "treeIds": [],
            "categoryId": ""
        };
        
        // 只覆盖四个动态参数，其他参数保持配置文件中的值
        return {
            ...baseParams,
            "studyPhaseCode": this.currentCombination.studyPhaseCode,
            "subjectCode": this.currentCombination.subjectCode,
            "textbookVersionCode": this.currentCombination.textbookVersionCode,
            "ceciCode": this.currentCombination.ceciCode
        };
    }
    
    // 切换到下一个参数组合
    async switchToNextCombination() {
        // 保存当前状态
        this.saveState();
        
        // 销毁当前浏览器池（清理所有cookie和会话状态）
        if (this.browserPool) {
            console.log('🌐 销毁当前浏览器池，清理所有cookie状态...');
            await this.browserPool.destroy();
            this.browserPool = null;
        }
        
        // 释放所有token的代理
        for (const tokenData of this.tokenData) {
            this.releaseProxyFromToken(tokenData.id);
        }
        
        this.config.currentCombinationIndex++;
        
        if (this.config.currentCombinationIndex >= this.config.paramsCombinations.length) {
            console.log('🎉 所有参数组合已处理完成！');
            return false; // 没有更多组合
        }
        
        console.log(`\n🔄 切换到下一个参数组合...`);
        
        // 重新初始化
        this.initParamsCombinations();
        this.ensureOutputDir();
        this.loadState(); // 加载新目录的状态
        
        // 重新初始化浏览器池（使用新的状态文件路径）
        this.setupBrowserPool();
        await this.browserPool.initialize();
        
        return true; // 成功切换
    }
    
    // 确保输出目录存在
    ensureOutputDir() {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
            console.log(`✅ 创建输出目录: ${this.outputDir}`);
        }
    }
    
    // 加载tokens文件
    loadTokens() {
        try {
            if (fs.existsSync(this.config.tokensFile)) {
                const tokensData = fs.readFileSync(this.config.tokensFile, 'utf8');
                const tokenConfig = JSON.parse(tokensData);
                
                if (tokenConfig.tokens && Array.isArray(tokenConfig.tokens)) {
                    this.tokenData = tokenConfig.tokens.filter(tokenObj => {
                        // 兼容旧格式（纯字符串）和新格式（对象）
                        if (typeof tokenObj === 'string') {
                            return tokenObj.trim();
                        } else if (tokenObj && tokenObj.token) {
                            return tokenObj.token.trim();
                        }
                        return false;
                    }).map((tokenObj, index) => {
                        // 统一转换为新格式
                        if (typeof tokenObj === 'string') {
                            return {
                                id: index + 1,
                                token: tokenObj,
                                isValid: null, // null=未验证, true=有效, false=永久无效, 'banned'=暂时被封禁
                                lastValidated: null,
                                bannedUntil: null // 被封禁到什么时候
                            };
                        } else {
                            // 确保所有必要的字段都存在，同时保留其他字段
                            return {
                                id: tokenObj.id || index + 1,
                                token: tokenObj.token,
                                isValid: tokenObj.isValid || null, // null=未验证, true=有效, false=永久无效, 'banned'=暂时被封禁
                                lastValidated: tokenObj.lastValidated || null,
                                bannedUntil: tokenObj.bannedUntil || null, // 被封禁到什么时候
                                // 保留其他字段，如phoneNumber等
                                ...Object.fromEntries(
                                    Object.entries(tokenObj).filter(([key]) => 
                                        !['id', 'token', 'isValid', 'lastValidated', 'bannedUntil'].includes(key)
                                    )
                                )
                            };
                        }
                    });
                    
                    console.log(`✅ 已加载 ${this.tokenData.length} 个tokens`);
                    
                    if (this.tokenData.length === 0) {
                        console.error('❌ tokens.json 中没有有效的token');
                    }
                } else {
                    console.error('❌ tokens.json 格式错误：缺少tokens数组');
                }
            } else {
                console.error(`❌ 未找到tokens文件: ${this.config.tokensFile}`);
            }
        } catch (error) {
            console.error('❌ 加载tokens失败:', error.message);
        }
    }
    
    // 保存tokens到文件
    saveTokens() {
        try {
            const tokenConfig = {
                tokens: this.tokenData,
                lastUpdated: new Date().toISOString(),
                description: "存储用于数据爬取的token列表，包含phoneNumber、验证状态等信息，支持多token并发请求"
            };

            fs.writeFileSync(this.config.tokensFile, JSON.stringify(tokenConfig, null, 4), 'utf8');
            console.log(`💾 已保存tokens配置到: ${this.config.tokensFile}`);
        } catch (error) {
            console.error('❌ 保存tokens失败:', error.message);
        }
    }
    
    // 加载状态文件
    loadState() {
        try {
            // 先检查已存在的JSON文件
            this.scanExistingFiles();
            
            // 然后加载状态文件
            if (fs.existsSync(this.stateFile)) {
                const stateData = fs.readFileSync(this.stateFile, 'utf8');
                const state = JSON.parse(stateData);
                
                // 恢复状态
                if (state.currentPage && state.currentPage > this.currentPage) {
                    this.currentPage = state.currentPage;
                }
                
                if (state.completedPages && Array.isArray(state.completedPages)) {
                    this.completedPages = new Set(state.completedPages);
                }
                
                // 恢复失败页面信息
                if (state.failedPages && Array.isArray(state.failedPages)) {
                    this.failedPages = new Set(state.failedPages);
                }
                
                // 恢复错误信息
                if (state.lastError) {
                    this.lastError = state.lastError;
                    console.log(`⚠️ 检测到上次运行的错误信息: ${state.lastError.message} (页面 ${state.lastError.page})`);
                }
                
                console.log(`🔄 已加载爬取状态: 当前页面 ${this.currentPage}, 已完成 ${this.completedPages.size} 页, 失败 ${this.failedPages.size} 页`);
                
            } else {
                console.log(`📋 未找到状态文件，从第 ${this.currentPage} 页开始`);
            }
            
            // 检查页面连续性
            this.checkAndFixPageContinuity();
            
        } catch (error) {
            console.error('⚠️ 加载状态文件失败:', error.message);
            console.log('📋 将从配置的起始页面开始');
        }
    }
    
    // 扫描已存在的文件
    scanExistingFiles() {
        try {
            if (fs.existsSync(this.outputDir)) {
                const files = fs.readdirSync(this.outputDir);
                const jsonFiles = files.filter(file => file.endsWith('.json') && !isNaN(parseInt(path.basename(file, '.json'))));
                
                for (const file of jsonFiles) {
                    const pageNum = parseInt(path.basename(file, '.json'));
                    if (!isNaN(pageNum)) {
                        this.completedPages.add(pageNum);
                        
                        // 更新当前页面为最大页面号+1
                        if (pageNum >= this.currentPage) {
                            this.currentPage = pageNum + 1;
                        }
                    }
                }
                
                if (jsonFiles.length > 0) {
                    console.log(`📁 发现 ${jsonFiles.length} 个已存在的数据文件`);
                }
            }
        } catch (error) {
            console.error('⚠️ 扫描已存在文件失败:', error.message);
        }
    }
    
    // 检查页面连续性并修复缺失页面
    checkAndFixPageContinuity() {
        if (this.completedPages.size === 0) {
            return;
        }
        
        const completedArray = Array.from(this.completedPages).sort((a, b) => a - b);
        const minPage = Math.max(this.config.startPage, completedArray[0]);
        const maxPage = completedArray[completedArray.length - 1];
        
        const missingPages = [];
        
        // 检查从最小页面到最大页面之间的连续性
        for (let page = minPage; page <= maxPage; page++) {
            if (!this.completedPages.has(page)) {
                missingPages.push(page);
            }
        }
        
        if (missingPages.length > 0) {
            console.log(`🔍 检测到 ${missingPages.length} 个缺失页面: [${missingPages.slice(0, 10).join(', ')}${missingPages.length > 10 ? '...' : ''}]`);
            
            // 将缺失页面添加到失败列表
            missingPages.forEach(page => {
                this.failedPages.add(page);
                this.completedPages.delete(page); // 确保从完成列表中移除
            });
            
            console.log(`📝 已将 ${missingPages.length} 个缺失页面加入重试队列`);
            
            // 更新当前页面为实际连续的最大页面+1
            let actualMaxPage = minPage - 1;
            for (let page = minPage; page <= maxPage; page++) {
                if (this.completedPages.has(page)) {
                    actualMaxPage = page;
                } else {
                    break;
                }
            }
            
            this.currentPage = Math.max(actualMaxPage + 1, this.currentPage);
            console.log(`🔄 调整当前页面为: ${this.currentPage}`);
        } else {
            console.log(`✅ 页面连续性检查通过 (${minPage}-${maxPage})`);
        }
    }
    
    // 保存状态文件
    saveState() {
        try {
            const state = {
                currentPage: this.currentPage,
                completedPages: Array.from(this.completedPages),
                failedPages: Array.from(this.failedPages), // 保存失败页面
                lastUpdateTime: new Date().toISOString(),
                currentCombination: this.currentCombination, // 保存当前参数组合信息
                combinationIndex: this.config.currentCombinationIndex, // 保存当前组合索引
                config: {
                    startPage: this.config.startPage,
                    pageSize: this.config.pageSize,
                    concurrency: this.config.concurrency
                }
            };
            
            // 如果有错误信息，也保存到状态中
            if (this.lastError) {
                state.lastError = this.lastError;
            }
            
            // 格式化JSON以便阅读
            fs.writeFileSync(this.stateFile, JSON.stringify(state, null, 2), 'utf8');
        } catch (error) {
            console.error('⚠️ 保存状态文件失败:', error.message);
        }
    }
    
    // Token 有效性检查
    async validateTokenData(tokenData) {
        if (!this.config.enableTokenValidation) {
            return true;
        }
        
        if (!tokenData || !tokenData.token) {
            console.error('❌ Token数据未提供');
            return false;
        }
        
        // 被封禁的token不进行验证
        if (tokenData.isValid === 'banned') {
            console.log(`⏸️ Token[${tokenData.id}] 被封禁中，跳过验证`);
            return false;
        }
        
        // 永久无效的token不进行验证
        if (tokenData.isValid === false) {
            return false;
        }
        
        // 检查是否需要验证token（基于验证间隔）
        const now = Date.now();
        const intervalMs = this.config.tokenValidationInterval * 60 * 1000;
        
        if (tokenData.lastValidated && 
            tokenData.isValid === true && 
            intervalMs > 0 && 
            (now - new Date(tokenData.lastValidated).getTime()) < intervalMs) {
            return true; // 在验证间隔内且已验证为有效
        }
        
        // 如果已有验证在进行中，等待结果
        if (this.tokenValidationPromise.has(tokenData.token)) {
            return await this.tokenValidationPromise.get(tokenData.token);
        }
        
        // 开始新的验证
        const validationPromise = this.validateToken(tokenData, 'validate');
        this.tokenValidationPromise.set(tokenData.token, validationPromise);
        const result = await validationPromise;
        this.tokenValidationPromise.delete(tokenData.token);
        
        return result.valid;
    }
    
    // 随机等待时间
    async randomDelay() {
        const delay = Math.floor(Math.random() * (this.config.maxDelay - this.config.minDelay + 1)) + this.config.minDelay;
        await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    // 构建请求数据
    buildRequestData(pageNum) {
        return {
            pageNum,
            pageSize: this.config.pageSize,
            params: this.getCurrentParams()
        };
    }
    
    // 发送单个请求（使用指定的已验证token和浏览器）
    async fetchPageWithToken(pageNum, tokenData, retryCount = 0) {
        if (!tokenData) {
            throw new Error('❌ 未提供有效的token数据');
        }
        
        // 检查是否已经爬取过此页面
        if (this.completedPages.has(pageNum)) {
            console.log(`⏭️ 第 ${pageNum} 页已存在，跳过`);
            return { success: true, hasData: true, count: 0, skipped: true };
        }
        
        let browserInstance = null;
        let page = null;
        let context = null;
        
        try {
            const retryInfo = retryCount > 0 ? ` (重试 ${retryCount}/${this.config.maxRetries})` : '';
            
            // 获取代理配置
            const proxyUrl = await this.assignProxyToToken(tokenData);
            
            // 为每个token获取专用的浏览器实例（确保cookie隔离）
            browserInstance = await this.browserPool.getBrowser(`token_${tokenData.id}`, proxyUrl);
            if (!browserInstance) {
                throw new Error('无法获取可用的浏览器实例');
            }
            
            // 创建页面并导航到目标URL（传入token用于设置cookie）
            try {
                const pageResult = await this.browserPool.createPage(browserInstance.browserId, 'https://zj.stzy.com/create-paper/chapter', tokenData.token);
                page = pageResult.page;
                context = pageResult.context;
            } catch (error) {
                console.error('❌ 创建页面失败:', error.message);
                throw error;
            }
            
            // 构建请求数据
            const requestData = this.buildRequestData(pageNum);
            
            // 验证cookie是否设置成功并获取所需的cookie
            const cookies = await context.cookies();
            const accessTokenCookie = cookies.find(cookie => cookie.name === 'ACCESS_TOKEN');
            
            // 获取所有cookie并构建cookie字符串
            const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
            
            // 在Node.js中使用axios发送API请求
            const axiosConfig = {
                timeout: 30000,
                headers: {
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,is;q=0.8,en;q=0.7',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Content-Type': 'application/json',
                    'Origin': 'https://zj.stzy.com',
                    'Pragma': 'no-cache',
                    'Referer': 'https://zj.stzy.com/',
                    'Sec-Fetch-Dest': 'empty',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Site': 'same-site',
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
                    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"macOS"',
                    'token': accessTokenCookie ? accessTokenCookie.value : tokenData.token,
                    'Cookie': cookieString
                }
            };
            
            // 如果有代理，使用httpsAgent
            if (proxyUrl) {
                axiosConfig.httpsAgent = new HttpsProxyAgent(proxyUrl);
            }
            
            const apiAxios = axios.create(axiosConfig);
            
            // 发送API请求
            const response = await apiAxios.post(this.config.baseURL, requestData);
            
            // 检查响应中的code字段
            if (response.data && response.data.code === 403) {
                await this.markTokenBanned(tokenData, '响应code=403，可能被暂时封禁');
                
                const error = new Error(`响应code=403，Token已被暂时封禁`);
                error.response = { status: 403 };
                throw error;
            }
            
            if (response?.data?.data?.list) {
                const list = response.data.data.list;
                
                // 如果总数 / 页数 小于当前页数，则认为没有更多数据
                if (list.length > 0 && pageNum <= (Number(response.data.data.total) / response.data.data.totalPage)) {
                    // 保存数据到文件
                    await this.savePageData(pageNum, list);
                    
                    // 标记为已完成
                    this.completedPages.add(pageNum);
                    
                    // 确保从失败列表中移除（成功时）
                    this.failedPages.delete(pageNum);
                    
                    // 清除错误信息（因为本次请求成功）
                    this.lastError = null;
                    
                    // 保存状态
                    this.saveState();
                    
                    const successInfo = retryCount > 0 ? ` (重试成功)` : '';
                    console.log(`💾 第 ${pageNum} 页保存成功 (${list.length} 条记录)${successInfo}`);
                    
                    return { success: true, hasData: true, count: list.length };
                } else {
                    console.log(`⚠️ 第 ${pageNum} 页没有数据`);
                    return { success: true, hasData: false, count: 0 };
                }
            } else {
                console.log(`⚠️ 第 ${pageNum} 页响应格式异常: ${JSON.stringify(response.data)}`);
                throw new Error(`响应格式异常: ${JSON.stringify(response.data)}`);
            }
            
        } catch (error) {
            console.error(`❌ Node.js请求第 ${pageNum} 页时出错:`, error.message);
            
            // 检查是否应该重试
            const shouldRetry = this.shouldRetry(error, retryCount);
            
            if (shouldRetry) {
                console.log(`🔄 第 ${pageNum} 页重试中...`);
                await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
                return this.fetchPageWithToken(pageNum, tokenData, retryCount + 1);
            }
            
            // 将失败的页面添加到失败列表
            this.failedPages.add(pageNum);
            
            // 保存异常信息到状态
            this.lastError = {
                timestamp: new Date().toISOString(),
                page: pageNum,
                message: error.message,
                stack: error.stack,
                statusCode: error.response ? error.response.status : null,
                tokenId: tokenData.id,
                retryCount: retryCount
            };
            
            // 根据错误类型提供更详细的错误信息
            let errorDetails = error.message;
            if (error.response) {
                const status = error.response.status;
                if (status === 503 || status === 429) {
                    console.log(`❌ 第 ${pageNum} 页失败: 服务器限制 (${status})`);
                } else if (status === 403 && error.message.includes('Token已被暂时封禁')) {
                    console.log(`❌ 第 ${pageNum} 页失败: Token[${tokenData.id}]被封禁`);
                } else {
                    console.log(`❌ 第 ${pageNum} 页失败: HTTP ${status}`);
                }
            } else {
                console.log(`❌ 第 ${pageNum} 页失败: ${error.message}`);
            }
            
            // 保存状态（包含异常信息和失败页面）
            this.saveState();
            
            return { success: false, hasData: false, count: 0, error: errorDetails };
            
        } finally {
            // 清理context防止内存泄漏
            try {
                if (context) {
                    await context.close();
                }
            } catch (cleanupError) {
                console.warn(`⚠️ 清理context时出错: ${cleanupError.message}`);
            }
            
            // 释放浏览器实例供后续请求使用
            if (browserInstance) {
                this.browserPool.releaseBrowser(browserInstance.browserId);
            }
        }
    }
    
    // 兼容旧接口的 fetchPage 方法
    async fetchPage(pageNum, retryCount = 0) {
        // 获取可用token
        const tokenData = await this.getAvailableToken();
        if (!tokenData) {
            throw new Error('❌ 没有可用的有效token');
        }
        
        // 验证token并发送请求
        const isValid = await this.validateTokenData(tokenData);
        if (!isValid) {
            throw new Error(`❌ Token[${tokenData.id}] 验证失败`);
        }
        
        return this.fetchPageWithToken(pageNum, tokenData, retryCount);
    }
    
    // 判断是否应该重试
    shouldRetry(error, retryCount) {
        // 如果已达到最大重试次数，不再重试
        if (retryCount >= this.config.maxRetries) {
            return false;
        }
        
        // 对于某些错误类型，不进行重试
        if (error.response) {
            const status = error.response.status;
            // 认证错误不重试
            if (status === 401) {
                return false;
            }
            if (status === 403) {
                // 如果是由response.data.code=403触发的403错误，允许重试（使用其他token）
                if (error.message.includes('Token已被暂时封禁')) {
                    return true;
                }
                return false;
            }
            // 客户端错误（4xx）通常不重试
            if (status >= 400 && status < 500 && status !== 429) {
                return false;
            }
            // 服务器错误（5xx）和频率限制（429）可以重试
            if (status >= 500 || status === 429) {
                return true;
            }
        }
        
        // 网络连接错误可以重试
        if (error.code === 'ECONNREFUSED' || 
            error.code === 'ENOTFOUND' || 
            error.code === 'ECONNRESET' || 
            error.code === 'ETIMEDOUT') {
            return true;
        }
        
        return false;
    }
    
    // 保存页面数据到JSON文件
    async savePageData(pageNum, data) {
        const filename = `${pageNum}.json`;
        const filepath = path.join(this.outputDir, filename);
        
        const jsonData = {
            page: pageNum,
            count: data.length,
            timestamp: new Date().toISOString(),
            data: data
        };
        
        return new Promise((resolve, reject) => {
            // 不格式化JSON以节省空间
            fs.writeFile(filepath, JSON.stringify(jsonData), 'utf8', (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }
    
    // 开始爬取
    async start() {
        console.log('🚀 开始数据爬取...');
        
        // 初始化浏览器池
        await this.browserPool.initialize();
        
        console.log(`📊 配置: ${this.currentCombination.studyPhaseName}/${this.currentCombination.subjectName}/${this.currentCombination.textbookVersionName}/${this.currentCombination.ceciName}`);
        console.log(`📋 状态: 当前页面 ${this.currentPage}, 已完成 ${this.completedPages.size} 页${this.failedPages.size > 0 ? `, 失败 ${this.failedPages.size} 页` : ''}`);
        console.log(`🪙 Tokens: ${this.tokenData.length} 个 (并发: ${this.config.concurrency})`);
        
        this.isRunning = true;
        const startTime = Date.now();
        
        try {
            await this.processConcurrentPages();
            
            const endTime = Date.now();
            const duration = (endTime - startTime) / 1000;
            
            console.log('');
            console.log('🎉 当前参数组合爬取完成!');
            console.log(`⏱️ 耗时: ${duration.toFixed(2)} 秒`);
            console.log(`📁 数据保存在: ${this.outputDir} 目录`);
            console.log(`📊 总计完成: ${this.completedPages.size} 页`);
            if (this.failedPages.size > 0) {
                console.log(`⚠️ 仍有失败: ${this.failedPages.size} 页 [${Array.from(this.failedPages).sort((a, b) => a - b).join(', ')}]`);
                console.log(`💡 下次运行时将自动重试失败的页面`);
            }
            
            // 检查是否所有参数组合都已完成
            if (this.config.currentCombinationIndex >= this.config.paramsCombinations.length - 1) {
                console.log('\n🎊 所有参数组合处理完成！');
            }
            
        } catch (error) {
            console.error('❌ 爬取过程中发生错误:', error);
        }
    }
    
    // 停止爬取
    async stop() {
        console.log('🛑 正在停止爬取...');
        this.isRunning = false;
        
        // 销毁浏览器池
        if (this.browserPool) {
            await this.browserPool.destroy();
            this.browserPool = null;
        }
        
        // 销毁代理池
        if (this.proxyPool) {
            this.proxyPool.destroy();
            this.proxyPool = null;
        }
        
        // 保存当前状态
        this.saveState();
        console.log(`💾 已保存爬取状态，下次可从第 ${this.currentPage} 页继续`);
    }
    
    // 重置状态（清除记忆）
    resetState() {
        try {
            if (fs.existsSync(this.stateFile)) {
                fs.unlinkSync(this.stateFile);
                console.log('🗑️ 已清除爬取状态');
            }
            this.completedPages.clear();
            this.failedPages.clear();
            this.currentPage = this.config.startPage;
            this.lastError = null;
            console.log('🔄 已重置到初始状态');
        } catch (error) {
            console.error('❌ 重置状态失败:', error.message);
        }
    }
    
    // 手动记录异常信息（用于测试或手动记录错误）
    recordError(errorMessage, page = null) {
        this.lastError = {
            timestamp: new Date().toISOString(),
            page: page || this.currentPage,
            message: errorMessage,
            stack: new Error().stack,
            manual: true // 标记为手动记录的错误
        };
        this.saveState();
        console.log(`⚠️ 已记录异常信息: ${errorMessage}`);
    }
}

// 使用示例
async function main() {
    // 配置参数 (优先从 config.js 加载，也可以在这里覆盖特定配置)
    const config = {
        // 如果需要覆盖 config.js 中的配置，可以在这里指定
        // 例如: token: 'override_token',
        // 例如: concurrency: 5,
    };
    
    console.log('🚀 开始批量参数组合爬取...');
    
    // 循环处理所有参数组合
    while (true) {
        // 创建爬虫实例
        const crawler = new DataCrawler(config);
        
        // 处理程序退出信号
        const signalHandler = async () => {
            console.log('\n收到中断信号，正在停止爬取...');
            await crawler.stop();
            process.exit(0);
        };
        
        process.on('SIGINT', signalHandler);
        
        try {
            // 开始爬取当前参数组合
            await crawler.start();
            
            // 检查是否还有下一个参数组合
            if (crawler.config.currentCombinationIndex >= crawler.config.paramsCombinations.length - 1) {
                console.log('\n🎊 所有参数组合处理完成！');
                break;
            }
            
            // 更新配置为下一个组合
            config.currentCombinationIndex = crawler.config.currentCombinationIndex + 1;
            
            console.log('\n⏳ 准备处理下一个参数组合...');
            await new Promise(resolve => setTimeout(resolve, 2000)); // 短暂等待
            
        } catch (error) {
            console.error('❌ 处理参数组合时发生错误:', error);
            break;
        } finally {
            // 移除信号处理器
            process.removeListener('SIGINT', signalHandler);
        }
    }
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = DataCrawler;
