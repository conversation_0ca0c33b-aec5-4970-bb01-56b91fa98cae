# 增强型网络爬虫系统

一个全面、生产就绪的网络爬虫系统，具备强大的错误处理、分布式状态管理和全面的监控功能。

## 🚀 功能特点

### 核心能力
- **强大的错误管理**：全面的错误分类与断路器模式
- **分布式状态管理**：原子化参数处理与受控并行
- **令牌生命周期管理**：自动令牌验证与健康监控
- **增强型网络爬虫**：容错 Playwright 爬虫与反机器人策略
- **实时健康监控**：系统健康跟踪、告警与建议
- **结构化日志**：JSON格式日志，多输出目标与轮转
- **配置管理**：环境感知设置与热重载功能

### 高级特性
- **断路器模式**：自动容错处理失败服务
- **指数退避**：智能重试策略与递增延迟
- **文件锁定**：使用锁文件的原子操作与冲突预防
- **心跳监控**：实例健康跟踪与超时清理
- **性能指标**：详细分析与响应时间跟踪
- **优雅关闭**：清理资源与状态保存

## 📁 项目结构

```
enhanced-scraping-system/
├── src/
│   ├── core/
│   │   ├── ConfigurationManager.js    # 集中配置管理
│   │   ├── ErrorManager.js           # 错误分类与恢复
│   │   └── Logger.js                 # 结构化日志系统
│   ├── managers/
│   │   ├── TokenLifecycleManager.js  # 令牌验证与管理
│   │   └── ParameterStateManager.js  # 参数状态协调
│   ├── scrapers/
│   │   └── EnhancedPlaywrightCrawler.js # 高级网络爬虫引擎
│   ├── monitoring/
│   │   └── HealthMonitor.js          # 系统健康监控
│   └── ScrapingOrchestrator.js       # 主协调系统
├── enhanced-config.js                # 系统配置
├── enhanced-main.js                  # 主入口点
└── ENHANCED-README.md               # 本文档
```

## 🛠️ 安装

1. **安装依赖**
   ```bash
   npm install playwright fs-extra
   ```

2. **安装 Playwright 浏览器**
   ```bash
   npx playwright install chromium
   ```

3. **创建必要目录**
   ```bash
   mkdir -p output logs temp
   ```

## ⚙️ 配置

系统使用 `enhanced-config.js` 进行配置。主要部分包括：

### 应用设置
```javascript
app: {
    name: '增强型网络爬虫系统',
    version: '2.0.0',
    environment: process.env.NODE_ENV || 'development'
}
```

### 文件路径
```javascript
files: {
    tokensFile: './tokens.json',
    paramsFile: './params.json',
    progressFile: './progress.json',
    outputDir: './output',
    logsDir: './logs'
}
```

### 浏览器配置
```javascript
browser: {
    headless: true,
    timeout: 100000,
    pageTimeout: 100000,
    maxInstances: 5
}
```

### 环境变量
- `NODE_ENV`：环境（development|production）
- `LOG_LEVEL`：日志级别（DEBUG|INFO|WARN|ERROR）
- `SCRAPER_HEADLESS`：浏览器无头模式运行
- `SCRAPER_MAX_CONCURRENT`：最大并发请求数
- `TOKEN_VALIDATION_INTERVAL`：令牌验证间隔（毫秒）
- `PARAM_BATCH_SIZE`：参数批处理大小

## 🚀 使用方法

### 基本用法
```bash
# 使用默认设置启动
node enhanced-main.js

# 使用自定义配置
node enhanced-main.js --config ./my-config.js

# 在生产模式下运行
node enhanced-main.js --environment production
```

### 系统监控
```bash
# 显示当前系统状态
node enhanced-main.js --status

# 显示详细健康报告
node enhanced-main.js --health

# 显示版本信息
node enhanced-main.js --version

# 显示帮助
node enhanced-main.js --help
```

### 编程使用
```javascript
const ScrapingOrchestrator = require('./src/ScrapingOrchestrator');

async function runScraping() {
    const orchestrator = new ScrapingOrchestrator('./enhanced-config.js', 'production');
    
    try {
        await orchestrator.initialize();
        await orchestrator.start();
    } catch (error) {
        console.error('爬取失败:', error);
        await orchestrator.shutdown();
    }
}

runScraping();
```

## 📊 数据文件

### tokens.json
```json
[
    {
        "token": "你的JWT令牌",
        "phone": "用户电话号码",
        "isValid": true,
        "lastValidated": "2024-01-01T00:00:00.000Z"
    }
]
```

### params.json
```json
[
    {
        "studyPhaseCode": "300",
        "subjectCode": "2",
        "textbookVersionCode": "30",
        "catalogCode": "143",
        "chapterCode": "1001",
        "sectionCode": "2001"
    }
]
```

## 🔧 组件详情

### ConfigurationManager（配置管理器）
- 环境感知配置加载
- 配置验证与热重载
- 集中设置管理

### ErrorManager（错误管理器）
- 全面错误分类
- 断路器模式实现
- 恢复策略确定
- 错误统计与报告

### Logger（日志记录器）
- 结构化JSON日志
- 多输出目标（控制台、文件、缓冲区）
- 日志轮转与归档
- 操作跟踪与性能指标

### TokenLifecycleManager（令牌生命周期管理器）
- 自动令牌验证与健康评分
- 令牌分配与轮换
- 使用分析与监控
- JWT结构验证

### ParameterStateManager（参数状态管理器）
- 分布式参数处理
- 使用文件锁的原子状态转换
- 实例协调与清理
- 批处理与受控并行

### EnhancedPlaywrightCrawler（增强型Playwright爬虫）
- 容错网络爬取
- 反机器人检测策略
- 请求/响应监控
- 浏览器资源管理

### HealthMonitor（健康监控器）
- 实时系统健康监控
- 性能指标收集
- 告警生成与管理
- 健康趋势分析与建议

## 📈 监控与告警

### 健康状态级别
- **健康**：所有系统正常运行
- **警告**：检测到轻微问题，系统功能正常
- **降级**：性能问题，功能减少
- **严重**：重大问题，需要立即关注

### 告警类型
- **内存**：高内存使用告警
- **CPU**：高CPU使用告警
- **组件**：组件特定健康告警
- **错误率**：高错误率告警

### 跟踪指标
- 请求成功/失败率
- 响应时间与性能
- 令牌健康与使用情况
- 参数处理进度
- 系统资源利用率

## 🛡️ 错误处理

### 错误类别
- **NETWORK**：网络连接问题
- **AUTHENTICATION**：令牌和认证问题
- **RATE_LIMIT**：速率限制和节流
- **TOKEN_EXPIRED**：令牌过期问题
- **BROWSER**：浏览器和页面错误
- **VALIDATION**：数据验证错误
- **FILE_SYSTEM**：文件操作错误

### 恢复策略
- **重试**：自动重试与指数退避
- **令牌轮换**：切换到不同令牌
- **断路器**：临时服务隔离
- **优雅降级**：减少功能模式

## 🔒 安全特性

- JWT令牌验证与健康监控
- 安全文件操作与原子写入
- 进程隔离与资源限制
- 全面审计日志
- 错误净化与安全报告

## 📝 日志记录

### 日志级别
- **DEBUG**：详细调试信息
- **INFO**：一般操作消息
- **WARN**：警告条件
- **ERROR**：错误条件
- **FATAL**：需要立即关注的严重错误

### 日志格式
- **JSON**：用于机器处理的结构化日志
- **文本**：用于开发的人类可读格式
- **控制台**：实时控制台输出
- **文件**：持久文件日志

## 🚦 性能优化

- 通过实例限制控制并发
- 浏览器资源池化与重用
- 智能重试策略
- 内存使用监控与清理
- 响应时间优化

## 🔄 状态管理

### 参数状态
- **PENDING**：等待处理
- **PROCESSING**：正在处理
- **COMPLETED**：成功处理
- **FAILED**：处理失败
- **RETRY**：标记为重试
- **OBSOLETE**：不再相关

### 状态转换
- 使用文件锁的原子状态更新
- 基于心跳的实例监控
- 自动清理过时状态
- 进度跟踪与报告

## 🛠️ 故障排除

### 常见问题

1. **高内存使用**
   - 减少并发实例
   - 增加内存限制
   - 检查内存泄漏

2. **令牌验证失败**
   - 验证令牌格式和过期时间
   - 检查网络连接
   - 审查认证端点

3. **浏览器启动失败**
   - 安装必要依赖
   - 检查系统资源
   - 验证浏览器权限

4. **文件锁超时**
   - 减少并发实例
   - 检查文件系统性能
   - 验证文件权限

### 调试模式
```bash
LOG_LEVEL=DEBUG node enhanced-main.js
```

### 健康检查
```bash
# 快速状态检查
node enhanced-main.js --status

# 详细健康报告
node enhanced-main.js --health
```

## 📞 支持

对于问题和疑问：
1. 查看故障排除部分
2. 查看 `logs/` 目录中的系统日志
3. 运行健康检查以识别问题
4. 检查配置设置

## 🔄 从旧系统迁移

要从现有系统迁移：

1. **备份当前数据**
   ```bash
   cp tokens.json tokens-backup.json
   cp params.json params-backup.json
   ```

2. **更新配置**
   - 从 `config.js` 复制设置到 `enhanced-config.js`
   - 根据需要调整文件路径和参数

3. **使用小数据集测试**
   - 从参数子集开始
   - 监控系统行为和性能

4. **逐步推出**
   - 逐渐增加批处理大小
   - 监控健康指标和告警

## 📄 许可证

此增强型网络爬虫系统为专有软件。保留所有权利。
