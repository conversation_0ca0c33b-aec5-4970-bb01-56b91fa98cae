#!/usr/bin/env node

/**
 * 清理过期实例记录的工具脚本
 * 用于解决频繁的 "🧹 Cleaned up stale instance" 警告日志
 */

const fs = require('fs');
const path = require('path');

const PROGRESS_FILE = './progress.json';
const INSTANCE_TIMEOUT = 1800000; // 30 minutes in milliseconds

function cleanupStaleInstances() {
    try {
        console.log('🧹 开始清理过期实例记录...');
        
        if (!fs.existsSync(PROGRESS_FILE)) {
            console.log('❌ 进度文件不存在:', PROGRESS_FILE);
            return;
        }

        // 读取进度文件
        const progressData = JSON.parse(fs.readFileSync(PROGRESS_FILE, 'utf8'));
        
        if (!progressData.instances) {
            console.log('✅ 没有实例记录需要清理');
            return;
        }

        const now = new Date();
        const instances = progressData.instances;
        let removedCount = 0;
        let keptCount = 0;

        console.log(`📊 当前实例总数: ${Object.keys(instances).length}`);

        // 遍历所有实例
        for (const [instanceId, instanceData] of Object.entries(instances)) {
            const lastHeartbeat = instanceData.lastHeartbeat ? new Date(instanceData.lastHeartbeat) : null;
            const startedAt = instanceData.startedAt ? new Date(instanceData.startedAt) : null;
            
            let shouldRemove = false;
            let reason = '';

            // 检查是否应该移除
            if (instanceData.status === 'stale') {
                if (lastHeartbeat) {
                    const timeSinceHeartbeat = now.getTime() - lastHeartbeat.getTime();
                    if (timeSinceHeartbeat > INSTANCE_TIMEOUT * 2) { // 超过1小时
                        shouldRemove = true;
                        reason = `心跳超时 ${Math.round(timeSinceHeartbeat / 60000)} 分钟`;
                    }
                } else if (startedAt) {
                    const timeSinceStart = now.getTime() - startedAt.getTime();
                    if (timeSinceStart > INSTANCE_TIMEOUT * 2) { // 超过1小时
                        shouldRemove = true;
                        reason = `启动超时 ${Math.round(timeSinceStart / 60000)} 分钟`;
                    }
                } else {
                    shouldRemove = true;
                    reason = '无时间戳记录';
                }
            } else if (instanceData.status === 'completed' || instanceData.status === 'failed') {
                // 已完成或失败的实例，如果超过24小时就移除
                const referenceTime = lastHeartbeat || startedAt;
                if (referenceTime) {
                    const timeSinceReference = now.getTime() - referenceTime.getTime();
                    if (timeSinceReference > 86400000) { // 24小时
                        shouldRemove = true;
                        reason = `已完成/失败超过24小时`;
                    }
                }
            }

            if (shouldRemove) {
                console.log(`🗑️  移除实例: ${instanceId} (${reason})`);
                delete instances[instanceId];
                removedCount++;
            } else {
                keptCount++;
            }
        }

        // 更新统计信息
        if (progressData.stats) {
            progressData.stats.totalInstances = keptCount;
            progressData.stats.activeInstances = Math.min(progressData.stats.activeInstances || 0, keptCount);
        }

        // 更新最后修改时间
        progressData.lastUpdated = now.toISOString();

        // 保存更新后的文件
        if (removedCount > 0) {
            fs.writeFileSync(PROGRESS_FILE, JSON.stringify(progressData, null, 2));
            console.log(`✅ 清理完成:`);
            console.log(`   - 移除实例: ${removedCount}`);
            console.log(`   - 保留实例: ${keptCount}`);
            console.log(`   - 总计节省: ${removedCount} 个实例记录`);
        } else {
            console.log('✅ 没有需要清理的过期实例');
        }

    } catch (error) {
        console.error('❌ 清理过程中出错:', error.message);
        console.error(error.stack);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    cleanupStaleInstances();
}

module.exports = { cleanupStaleInstances };
