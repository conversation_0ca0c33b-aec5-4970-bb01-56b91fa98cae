const EnhancedPlaywrightCrawler = require('./src/scrapers/EnhancedPlaywrightCrawler');
const TokenLifecycleManager = require('./src/managers/TokenLifecycleManager');
const Logger = require('./src/core/Logger');

async function testNavigation() {
    const logger = new Logger('test');
    let crawler = null;

    try {
        // 创建令牌管理器
        const tokenManager = new TokenLifecycleManager('./tokens.txt', logger);
        await tokenManager.initialize();

        // 创建爬虫实例
        crawler = new EnhancedPlaywrightCrawler({
            baseURL: 'https://zj.stzy.com/create-paper/chapter',
            maxRetries: 2,
            requestTimeout: 30000,
            pageTimeout: 30000
        }, tokenManager, logger);
        
        // 测试参数
        const testParameters = {
            studyPhaseCode: '1',
            subjectCode: '1',
            textbookVersionCode: '1',
            catalogCode: '1',
            chapterCode: '1',
            sectionCode: '1',
            studyPhaseName: '测试学段',
            subjectName: '测试科目',
            textbookVersionName: '测试版本',
            ceciName: '测试册次',
            catalogName: '测试目录'
        };
        
        console.log('🚀 开始测试导航功能...');
        
        // 创建浏览器和页面
        await crawler.createBrowserAndPageWithToken();
        console.log('✅ 浏览器创建成功');
        
        // 测试导航
        const page = crawler.mainPage;
        if (!page) {
            throw new Error('主页面不可用');
        }
        
        console.log('📍 当前页面URL:', page.url());
        
        // 导航到目标页面
        await crawler.navigateToPage(page, testParameters);
        console.log('✅ 导航完成');
        console.log('📍 导航后页面URL:', page.url());
        
        // 等待一下让页面加载
        await page.waitForTimeout(3000);
        
        // 检查页面标题
        const title = await page.title();
        console.log('📄 页面标题:', title);
        
        // 检查是否有关键元素
        const hasTextbookTree = await page.locator('#textbook_tree').isVisible().catch(() => false);
        const hasPagination = await page.locator('ul.ant-pagination').isVisible().catch(() => false);
        
        console.log('🔍 页面元素检查:');
        console.log('  - 教材树:', hasTextbookTree ? '✅ 存在' : '❌ 不存在');
        console.log('  - 分页组件:', hasPagination ? '✅ 存在' : '❌ 不存在');
        
        if (page.url() !== 'about:blank') {
            console.log('🎉 导航测试成功！页面已正确加载');
        } else {
            console.log('❌ 导航测试失败！页面仍然是 about:blank');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    } finally {
        // 清理资源
        if (crawler) {
            await crawler.cleanup();
        }
    }
}

// 运行测试
testNavigation().catch(console.error);
