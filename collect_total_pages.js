const axios = require('axios');
const fs = require('fs');
const { HttpsProxyAgent } = require('https-proxy-agent');

const PROXY_CONFIG = 'd2196405406:triucylw@61.184.8.27:41450'; // 可通过环境变量设置

// 设置axios全局代理配置
if (PROXY_CONFIG) {
    const proxyUrl = `http://${PROXY_CONFIG}`;
    const agent = new HttpsProxyAgent(proxyUrl);
    
    // 设置全局代理
    axios.defaults.httpsAgent = agent;
    axios.defaults.httpAgent = agent;
    
    // 设置默认配置
    axios.defaults.proxy = false; // 禁用默认代理，使用自定义agent
    
    console.log(`已配置代理: ${PROXY_CONFIG.replace(/:[^:]*@/, ':****@')}`);
    
    // 测试代理连接
    axios.get('https://httpbin.org/ip', { timeout: 10000 })
        .then(response => {
            console.log('代理测试成功，当前IP:', response.data.origin);
        })
        .catch(error => {
            console.log('代理测试失败:', error.message);
        });
}

// API请求函数
const getTextbookQueryTotalPage = async (params) => {
    try {
        // 构建请求数据，参考index.js中的buildRequestData方法
        const requestData = {
            pageNum: 1,
            pageSize: 20,
            params: {
                "searchType": 1,
                "sort": 0,
                "yearCode": "",
                "gradeCode": "",
                "provinceCode": "",
                "cityCode": "",
                "areaCode": "",
                "organizationCode": "",
                "termCode": "",
                "keyWord": "",
                "filterQuestionFlag": false,
                "searchScope": 0,
                "treeIds": [params.catalogCode],
                "categoryId": "",
                "studyPhaseCode": params.studyPhaseCode,
                "subjectCode": params.subjectCode,
                "textbookVersionCode": params.textbookVersionCode,
                "ceciCode": params.ceciCode
            }
        };

        const response = await axios.post('https://qms.stzy.com/matrix/zw-search/api/v1/homeEs/question/textbookQuery', requestData, {
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        if (response.data && response.data.data && typeof response.data.data.totalPage !== 'undefined') {
            return response.data.data.totalPage;
        } else {
            console.warn('返回数据格式异常:', response.data);
            return null;
        }
    } catch (error) {
        console.error(`请求失败 - ${params.studyPhaseName}/${params.subjectName}/${params.textbookVersionName}/${params.ceciName}/${params.catalogName}:`, error.message);
        return null;
    }
};

// 延迟函数
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 主函数
const main = async () => {
    // 等待代理测试完成
    if (PROXY_CONFIG) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('开始执行主要逻辑...\n');
    }

    // 读取参数文件
    let paramsData;
    try {
        const paramsContent = fs.readFileSync('params.json', 'utf8');
        paramsData = JSON.parse(paramsContent);
        console.log(`✅ 成功加载参数文件，共 ${paramsData.length} 个参数组合`);
    } catch (error) {
        console.error('❌ 读取参数文件失败:', error.message);
        return;
    }

    const results = [];
    const totalCount = paramsData.length;
    let processedCount = 0;
    let successCount = 0;
    let failureCount = 0;

    console.log(`🚀 开始处理 ${totalCount} 个参数组合...\n`);

    for (let i = 0; i < paramsData.length; i++) {
        const params = paramsData[i];
        processedCount++;

        console.log(`[${processedCount}/${totalCount}] 处理: ${params.studyPhaseName}/${params.subjectName}/${params.textbookVersionName}/${params.ceciName}/${params.catalogName}`);

        const totalPage = await getTextbookQueryTotalPage(params);
        
        const result = {
            ...params,
            totalPage: totalPage,
            requestTime: new Date().toISOString(),
            status: totalPage !== null ? 'success' : 'failed'
        };

        results.push(result);

        if (totalPage !== null) {
            successCount++;
            console.log(`   ✅ 成功获取 totalPage: ${totalPage}`);
        } else {
            failureCount++;
            console.log(`   ❌ 获取失败`);
        }

        // 每处理10个参数就保存一次中间结果
        if (processedCount % 10 === 0) {
            const tempFilename = `total_pages_temp_${processedCount}.json`;
            fs.writeFileSync(tempFilename, JSON.stringify(results, null, 2));
            console.log(`💾 已保存中间结果到: ${tempFilename}`);
        }

        // 请求间隔，避免过于频繁
        await delay(500 + Math.random() * 1000); // 500-1500ms随机延迟

        // 每100个请求显示进度统计
        if (processedCount % 100 === 0) {
            const progress = ((processedCount / totalCount) * 100).toFixed(1);
            console.log(`\n📊 进度统计:`);
            console.log(`   进度: ${processedCount}/${totalCount} (${progress}%)`);
            console.log(`   成功: ${successCount}, 失败: ${failureCount}`);
            console.log(`   成功率: ${((successCount / processedCount) * 100).toFixed(1)}%\n`);
        }
    }

    // 生成最终结果
    const summary = {
        metadata: {
            totalRequests: totalCount,
            successfulRequests: successCount,
            failedRequests: failureCount,
            successRate: ((successCount / totalCount) * 100).toFixed(2) + '%',
            processedAt: new Date().toISOString(),
            proxyUsed: !!PROXY_CONFIG
        },
        results: results,
        statistics: {
            totalPagesByPhase: {},
            totalPagesBySubject: {},
            totalPagesByCombination: {}
        }
    };

    // 统计各维度的totalPage总和
    results.forEach(result => {
        if (result.totalPage !== null) {
            // 按学段统计
            const phase = result.studyPhaseName;
            if (!summary.statistics.totalPagesByPhase[phase]) {
                summary.statistics.totalPagesByPhase[phase] = 0;
            }
            summary.statistics.totalPagesByPhase[phase] += result.totalPage;

            // 按学科统计
            const subject = `${result.studyPhaseName}-${result.subjectName}`;
            if (!summary.statistics.totalPagesBySubject[subject]) {
                summary.statistics.totalPagesBySubject[subject] = 0;
            }
            summary.statistics.totalPagesBySubject[subject] += result.totalPage;

            // 按完整组合统计
            const combination = `${result.studyPhaseName}/${result.subjectName}/${result.textbookVersionName}/${result.ceciName}`;
            if (!summary.statistics.totalPagesByCombination[combination]) {
                summary.statistics.totalPagesByCombination[combination] = 0;
            }
            summary.statistics.totalPagesByCombination[combination] += result.totalPage;
        }
    });

    // 保存最终结果
    const outputFilename = `total_pages_summary_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.json`;
    fs.writeFileSync(outputFilename, JSON.stringify(summary, null, 2));

    console.log(`\n🎉 处理完成！`);
    console.log(`📊 最终统计:`);
    console.log(`   总请求数: ${totalCount}`);
    console.log(`   成功请求: ${successCount}`);
    console.log(`   失败请求: ${failureCount}`);
    console.log(`   成功率: ${((successCount / totalCount) * 100).toFixed(2)}%`);
    console.log(`💾 结果已保存到: ${outputFilename}`);

    // 清理临时文件
    try {
        const tempFiles = fs.readdirSync('.').filter(file => file.startsWith('total_pages_temp_'));
        tempFiles.forEach(file => {
            fs.unlinkSync(file);
            console.log(`🗑️ 已清理临时文件: ${file}`);
        });
    } catch (error) {
        console.warn('清理临时文件时出错:', error.message);
    }
};

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
});

process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    process.exit(1);
});

// 优雅退出处理
process.on('SIGINT', () => {
    console.log('\n收到中断信号，正在保存当前进度...');
    process.exit(0);
});

// 运行主函数
main().catch(error => {
    console.error('主函数执行失败:', error);
    process.exit(1);
}); 