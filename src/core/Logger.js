const fs = require('fs');
const path = require('path');

/**
 * Structured Logging System
 * Provides comprehensive logging with multiple levels, formats, and outputs
 */
class Logger {
    constructor(config = {}) {
        this.config = {
            level: config.level || 'INFO',
            format: config.format || 'json',
            maxFileSize: config.maxFileSize || '10MB',
            maxFiles: config.maxFiles || 5,
            enableConsole: config.enableConsole !== false,
            enableFile: config.enableFile !== false,
            enableStructured: config.enableStructured !== false,
            logsDir: config.logsDir || './logs',
            instanceId: config.instanceId || `logger_${process.pid}`,
            ...config
        };

        this.levels = {
            DEBUG: 0,
            INFO: 1,
            WARN: 2,
            ERROR: 3,
            FATAL: 4
        };

        this.currentLevel = this.levels[this.config.level] || this.levels.INFO;
        this.logBuffer = [];
        this.fileStreams = new Map();
        
        this.setupLogging();
        this.setupProcessHandlers();
    }

    /**
     * Setup logging infrastructure
     */
    setupLogging() {
        // Ensure logs directory exists
        if (this.config.enableFile && !fs.existsSync(this.config.logsDir)) {
            fs.mkdirSync(this.config.logsDir, { recursive: true });
        }

        // Setup file rotation
        if (this.config.enableFile) {
            this.setupFileRotation();
        }

        console.log(`✅ Logger initialized - Level: ${this.config.level}, Format: ${this.config.format}`);
    }

    /**
     * Setup file rotation for log files
     */
    setupFileRotation() {
        const logFile = path.join(this.config.logsDir, 'scraper.log');
        const errorFile = path.join(this.config.logsDir, 'error.log');

        // Check if rotation is needed
        this.checkAndRotateFile(logFile);
        this.checkAndRotateFile(errorFile);

        // Setup periodic rotation check
        setInterval(() => {
            this.checkAndRotateFile(logFile);
            this.checkAndRotateFile(errorFile);
        }, 60000); // Check every minute
    }

    /**
     * Check if file needs rotation and rotate if necessary
     */
    checkAndRotateFile(filePath) {
        if (!fs.existsSync(filePath)) return;

        const stats = fs.statSync(filePath);
        const maxSize = this.parseFileSize(this.config.maxFileSize);

        if (stats.size > maxSize) {
            this.rotateFile(filePath);
        }
    }

    /**
     * Parse file size string to bytes
     */
    parseFileSize(sizeStr) {
        const units = { B: 1, KB: 1024, MB: 1024 * 1024, GB: 1024 * 1024 * 1024 };
        const match = sizeStr.match(/^(\d+)(B|KB|MB|GB)$/i);
        if (!match) return 10 * 1024 * 1024; // Default 10MB
        return parseInt(match[1]) * units[match[2].toUpperCase()];
    }

    /**
     * Rotate log file
     */
    rotateFile(filePath) {
        const dir = path.dirname(filePath);
        const ext = path.extname(filePath);
        const basename = path.basename(filePath, ext);

        // Close existing stream if open
        if (this.fileStreams.has(filePath)) {
            this.fileStreams.get(filePath).end();
            this.fileStreams.delete(filePath);
        }

        // Rotate existing files
        for (let i = this.config.maxFiles - 1; i > 0; i--) {
            const oldFile = path.join(dir, `${basename}.${i}${ext}`);
            const newFile = path.join(dir, `${basename}.${i + 1}${ext}`);
            
            if (fs.existsSync(oldFile)) {
                if (i === this.config.maxFiles - 1) {
                    fs.unlinkSync(oldFile); // Delete oldest file
                } else {
                    fs.renameSync(oldFile, newFile);
                }
            }
        }

        // Move current file to .1
        const rotatedFile = path.join(dir, `${basename}.1${ext}`);
        fs.renameSync(filePath, rotatedFile);

        console.log(`📁 Log file rotated: ${filePath} -> ${rotatedFile}`);
    }

    /**
     * Get or create file stream
     */
    getFileStream(filePath) {
        if (!this.fileStreams.has(filePath)) {
            const stream = fs.createWriteStream(filePath, { flags: 'a' });
            this.fileStreams.set(filePath, stream);
        }
        return this.fileStreams.get(filePath);
    }

    /**
     * Format log entry
     */
    formatLogEntry(level, message, meta = {}) {
        const timestamp = new Date().toISOString();
        const baseEntry = {
            timestamp,
            level,
            instanceId: this.config.instanceId,
            message,
            ...meta
        };

        if (this.config.format === 'json') {
            return JSON.stringify(baseEntry);
        } else {
            const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
            return `[${timestamp}] ${level} [${this.config.instanceId}] ${message}${metaStr}`;
        }
    }

    /**
     * Write log entry to outputs
     */
    writeLog(level, formattedEntry) {
        // Console output with beautified formatting
        if (this.config.enableConsole) {
            const beautifiedOutput = this.beautifyConsoleOutput(level, formattedEntry);
            const consoleMethod = level === 'ERROR' || level === 'FATAL' ? 'error' :
                                 level === 'WARN' ? 'warn' : 'log';
            console[consoleMethod](beautifiedOutput);
        }

        // File output (keep original JSON format for files)
        if (this.config.enableFile) {
            const logFile = path.join(this.config.logsDir, 'scraper.log');
            const stream = this.getFileStream(logFile);
            stream.write(formattedEntry + '\n');

            // Also write errors to separate error log
            if (level === 'ERROR' || level === 'FATAL') {
                const errorFile = path.join(this.config.logsDir, 'error.log');
                const errorStream = this.getFileStream(errorFile);
                errorStream.write(formattedEntry + '\n');
            }
        }
    }

    /**
     * Beautify console output for better readability
     */
    beautifyConsoleOutput(level, formattedEntry) {
        try {
            // If it's JSON format, parse and beautify
            if (this.config.format === 'json') {
                const logData = JSON.parse(formattedEntry);
                return this.formatBeautifiedConsole(level, logData);
            } else {
                // Already formatted as text
                return formattedEntry;
            }
        } catch (error) {
            // If parsing fails, return original
            return formattedEntry;
        }
    }

    /**
     * Format beautified console output
     */
    formatBeautifiedConsole(level, logData) {
        const colors = {
            DEBUG: '\x1b[36m',   // Cyan
            INFO: '\x1b[32m',    // Green
            WARN: '\x1b[33m',    // Yellow
            ERROR: '\x1b[31m',   // Red
            FATAL: '\x1b[35m',   // Magenta
            RESET: '\x1b[0m'     // Reset
        };

        const levelColor = colors[level] || colors.INFO;
        const resetColor = colors.RESET;

        // Format timestamp
        const timestamp = new Date(logData.timestamp).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        // Build beautified output
        let output = `${levelColor}[${timestamp}] ${level}${resetColor} `;

        // Add instance ID if available
        if (logData.instanceId) {
            output += `\x1b[90m[${logData.instanceId}]\x1b[0m `;
        }

        // Add main message
        output += logData.message;

        // Add metadata if present
        const metaKeys = Object.keys(logData).filter(key =>
            !['timestamp', 'level', 'instanceId', 'message'].includes(key)
        );

        if (metaKeys.length > 0) {
            const metaData = {};
            metaKeys.forEach(key => {
                metaData[key] = logData[key];
            });

            // Format metadata nicely
            const metaStr = this.formatMetadata(metaData);
            if (metaStr) {
                output += ` \x1b[90m${metaStr}\x1b[0m`;
            }
        }

        return output;
    }

    /**
     * Format metadata for console output
     */
    formatMetadata(metadata) {
        try {
            const formatted = [];

            for (const [key, value] of Object.entries(metadata)) {
                if (value === null || value === undefined) continue;

                if (typeof value === 'object') {
                    // For objects, show key properties
                    if (Array.isArray(value)) {
                        formatted.push(`${key}=[${value.length} items]`);
                    } else {
                        const keys = Object.keys(value);
                        if (keys.length <= 3) {
                            formatted.push(`${key}=${JSON.stringify(value)}`);
                        } else {
                            formatted.push(`${key}={${keys.length} props}`);
                        }
                    }
                } else {
                    formatted.push(`${key}=${value}`);
                }
            }

            return formatted.length > 0 ? `(${formatted.join(', ')})` : '';
        } catch (error) {
            return `(metadata: ${Object.keys(metadata).length} props)`;
        }
    }

    /**
     * Log message with specified level
     */
    log(level, message, meta = {}) {
        if (this.levels[level] < this.currentLevel) {
            return; // Skip if below current log level
        }

        const formattedEntry = this.formatLogEntry(level, message, meta);
        this.writeLog(level, formattedEntry);

        // Add to buffer for structured logging
        if (this.config.enableStructured) {
            this.logBuffer.push({
                timestamp: new Date().toISOString(),
                level,
                message,
                meta
            });

            // Keep buffer size manageable
            if (this.logBuffer.length > 1000) {
                this.logBuffer = this.logBuffer.slice(-1000);
            }
        }
    }

    /**
     * Debug level logging
     */
    debug(message, meta = {}) {
        this.log('DEBUG', message, meta);
    }

    /**
     * Info level logging
     */
    info(message, meta = {}) {
        this.log('INFO', message, meta);
    }

    /**
     * Warning level logging
     */
    warn(message, meta = {}) {
        this.log('WARN', message, meta);
    }

    /**
     * Error level logging
     */
    error(message, meta = {}) {
        this.log('ERROR', message, meta);
    }

    /**
     * Fatal level logging
     */
    fatal(message, meta = {}) {
        this.log('FATAL', message, meta);
    }

    /**
     * Log operation start
     */
    startOperation(operationName, meta = {}) {
        const operationId = `op_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
        this.info(`🚀 Starting operation: ${operationName}`, {
            operation: operationName,
            operationId,
            phase: 'start',
            ...meta
        });
        return operationId;
    }

    /**
     * Log operation completion
     */
    completeOperation(operationName, operationId, meta = {}) {
        this.info(`✅ Completed operation: ${operationName}`, {
            operation: operationName,
            operationId,
            phase: 'complete',
            ...meta
        });
    }

    /**
     * Log operation failure
     */
    failOperation(operationName, operationId, error, meta = {}) {
        this.error(`❌ Failed operation: ${operationName}`, {
            operation: operationName,
            operationId,
            phase: 'failed',
            error: error.message || error.toString(),
            ...meta
        });
    }

    /**
     * Log progress update
     */
    progress(message, current, total, meta = {}) {
        const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
        this.info(`📊 ${message}`, {
            progress: {
                current,
                total,
                percentage,
                remaining: total - current
            },
            ...meta
        });
    }

    /**
     * Log performance metrics
     */
    metrics(operation, duration, meta = {}) {
        this.info(`⏱️ Performance: ${operation}`, {
            performance: {
                operation,
                duration,
                timestamp: new Date().toISOString()
            },
            ...meta
        });
    }

    /**
     * Get recent log entries
     */
    getRecentLogs(count = 100, level = null) {
        let logs = this.logBuffer.slice(-count);
        
        if (level) {
            logs = logs.filter(log => log.level === level);
        }
        
        return logs;
    }

    /**
     * Get log statistics
     */
    getStatistics() {
        const now = Date.now();
        const oneHourAgo = now - 3600000;
        
        const recentLogs = this.logBuffer.filter(
            log => new Date(log.timestamp).getTime() > oneHourAgo
        );

        const levelCounts = {};
        for (const level of Object.keys(this.levels)) {
            levelCounts[level] = recentLogs.filter(log => log.level === level).length;
        }

        return {
            totalLogs: this.logBuffer.length,
            recentLogs: recentLogs.length,
            levelCounts,
            errorRate: levelCounts.ERROR / Math.max(recentLogs.length, 1)
        };
    }

    /**
     * Setup process handlers for graceful shutdown
     */
    setupProcessHandlers() {
        const cleanup = () => {
            this.info('🔄 Logger shutting down...');
            
            // Close all file streams
            for (const stream of this.fileStreams.values()) {
                stream.end();
            }
            
            this.fileStreams.clear();
        };

        process.on('SIGINT', cleanup);
        process.on('SIGTERM', cleanup);
        process.on('exit', cleanup);
    }

    /**
     * Change log level dynamically
     */
    setLevel(level) {
        if (this.levels[level] !== undefined) {
            this.currentLevel = this.levels[level];
            this.config.level = level;
            this.info(`📝 Log level changed to: ${level}`);
        } else {
            this.warn(`❌ Invalid log level: ${level}`);
        }
    }

    /**
     * Flush all pending logs
     */
    flush() {
        for (const stream of this.fileStreams.values()) {
            if (stream.writable) {
                stream.write('');
            }
        }
    }
}

module.exports = Logger;
