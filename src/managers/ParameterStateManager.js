const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * Enhanced Parameter State Management System
 * Provides robust state management for parameter combinations with controlled parallelism
 */
class ParameterStateManager {
    constructor(config = {}) {
        this.config = {
            paramsFile: config.paramsFile || './params.json',
            progressFile: config.progressFile || './progress.json',
            maxConcurrentInstances: config.maxConcurrentInstances || 5,
            instanceTimeout: config.instanceTimeout || 1800000, // 30 minutes
            stateCheckInterval: config.stateCheckInterval || 30000, // 30 seconds
            lockTimeout: config.lockTimeout || 30000,
            batchSize: config.batchSize || 10,
            instanceId: config.instanceId || `param_manager_${process.pid}_${crypto.randomBytes(4).toString('hex')}`,
            ...config
        };

        this.logger = config.logger;
        this.errorManager = config.errorManager;

        // State management
        this.parameters = [];
        this.progress = new Map();
        this.instanceStates = new Map();
        this.processingQueue = [];
        
        // File locking
        this.lockFile = `${this.config.progressFile}.lock`;
        
        // Timers
        this.stateCheckTimer = null;
        this.cleanupTimer = null;

        this.initialize();
    }

    /**
     * Parameter processing states
     */
    static STATES = {
        PENDING: 'pending',
        PROCESSING: 'processing', 
        COMPLETED: 'completed',
        FAILED: 'failed',
        OBSOLETE: 'obsolete',
        RETRY: 'retry'
    };

    /**
     * Initialize parameter state manager
     */
    async initialize() {
        try {
            await this.loadParameters();
            await this.loadProgress();
            await this.registerInstance();
            this.startPeriodicTasks();
            
            this.logger?.info('✅ Parameter State Manager initialized', {
                totalParams: this.parameters.length,
                pendingParams: this.getPendingCount(),
                instanceId: this.config.instanceId
            });
        } catch (error) {
            this.logger?.error('❌ Failed to initialize Parameter State Manager', { error: error.message });
            throw error;
        }
    }

    /**
     * Load parameters from file
     */
    async loadParameters() {
        if (!fs.existsSync(this.config.paramsFile)) {
            throw new Error(`Parameters file not found: ${this.config.paramsFile}`);
        }

        const data = fs.readFileSync(this.config.paramsFile, 'utf8');
        const paramData = JSON.parse(data);
        
        this.parameters = paramData.params || paramData || [];
        
        // Generate unique keys for each parameter combination
        this.parameters.forEach((param, index) => {
            if (!param.key) {
                param.key = this.generateParameterKey(param, index);
            }
            if (!param.id) {
                param.id = index + 1;
            }
        });

        this.logger?.info('📥 Parameters loaded', { count: this.parameters.length });
    }

    /**
     * Generate unique key for parameter combination
     */
    generateParameterKey(param, index) {
        // Create a consistent key based on parameter values
        const keyParts = [
            param.studyPhaseCode || '',
            param.subjectCode || '',
            param.textbookVersionCode || '',
            param.catalogCode || '',
            param.chapterCode || '',
            param.sectionCode || ''
        ];
        
        const keyString = keyParts.join('_');
        return keyString || `param_${index + 1}`;
    }

    /**
     * Load progress from file
     */
    async loadProgress() {
        this.progress.clear();
        
        if (!fs.existsSync(this.config.progressFile)) {
            // Create initial progress file
            await this.saveProgress();
            return;
        }

        if (!await this.acquireLock()) {
            throw new Error('Failed to acquire lock for loading progress');
        }

        try {
            const data = fs.readFileSync(this.config.progressFile, 'utf8');
            const progressData = JSON.parse(data);
            
            // Load parameter states
            const paramStates = progressData.parameters || {};
            for (const [key, state] of Object.entries(paramStates)) {
                this.progress.set(key, {
                    key: key,
                    status: state.status || ParameterStateManager.STATES.PENDING,
                    instanceId: state.instanceId || null,
                    startedAt: state.startedAt ? new Date(state.startedAt) : null,
                    completedAt: state.completedAt ? new Date(state.completedAt) : null,
                    attempts: state.attempts || 0,
                    lastError: state.lastError || null,
                    result: state.result || null,
                    metadata: state.metadata || {}
                });
            }

            // Load instance states
            const instances = progressData.instances || {};
            for (const [instanceId, state] of Object.entries(instances)) {
                this.instanceStates.set(instanceId, {
                    instanceId: instanceId,
                    status: state.status || 'unknown',
                    lastHeartbeat: state.lastHeartbeat ? new Date(state.lastHeartbeat) : null,
                    assignedParams: state.assignedParams || [],
                    startedAt: state.startedAt ? new Date(state.startedAt) : null,
                    metadata: state.metadata || {}
                });
            }

            this.logger?.info('📥 Progress loaded', {
                paramStates: this.progress.size,
                instances: this.instanceStates.size
            });

        } finally {
            this.releaseLock();
        }
    }

    /**
     * Register current instance
     */
    async registerInstance() {
        const instanceState = {
            instanceId: this.config.instanceId,
            status: 'active',
            lastHeartbeat: new Date(),
            assignedParams: [],
            startedAt: new Date(),
            metadata: {
                pid: process.pid,
                nodeVersion: process.version,
                platform: process.platform
            }
        };

        this.instanceStates.set(this.config.instanceId, instanceState);
        await this.saveProgress();
        
        this.logger?.info('📝 Instance registered', { instanceId: this.config.instanceId });
    }

    /**
     * Get next batch of parameters to process
     */
    async getNextBatch(batchSize = null) {
        const actualBatchSize = batchSize || this.config.batchSize;
        
        if (!await this.acquireLock()) {
            throw new Error('Failed to acquire lock for getting next batch');
        }

        try {
            // Check if we can process more parameters
            const activeInstances = this.getActiveInstanceCount();
            this.logger?.info('🔧 Instance count check', {
                activeInstances,
                maxConcurrentInstances: this.config.maxConcurrentInstances,
                totalInstances: this.instanceStates.size,
                currentInstanceId: this.config.instanceId
            });

            if (activeInstances >= this.config.maxConcurrentInstances) {
                this.logger?.info('⚠️ Maximum concurrent instances reached', {
                    active: activeInstances,
                    max: this.config.maxConcurrentInstances
                });
                return [];
            }

            // Clean up stale processing states
            await this.cleanupStaleProcessing();

            // Find pending parameters
            const pendingParams = [];
            for (const param of this.parameters) {
                const state = this.progress.get(param.key);
                
                if (!state || state.status === ParameterStateManager.STATES.PENDING || 
                    state.status === ParameterStateManager.STATES.RETRY) {
                    pendingParams.push(param);
                    
                    if (pendingParams.length >= actualBatchSize) {
                        break;
                    }
                }
            }

            // Mark parameters as processing
            const batch = [];
            for (const param of pendingParams) {
                this.setParameterState(param.key, ParameterStateManager.STATES.PROCESSING, {
                    instanceId: this.config.instanceId,
                    startedAt: new Date()
                });
                batch.push(param);
            }

            // Update instance state
            const instanceState = this.instanceStates.get(this.config.instanceId);
            if (instanceState) {
                instanceState.assignedParams = batch.map(p => p.key);
                instanceState.lastHeartbeat = new Date();
            }

            await this.saveProgress();
            
            this.logger?.info('📦 Batch assigned', {
                batchSize: batch.length,
                instanceId: this.config.instanceId,
                remaining: this.getPendingCount() - batch.length
            });

            return batch;

        } finally {
            this.releaseLock();
        }
    }

    /**
     * Set parameter state
     */
    setParameterState(paramKey, status, metadata = {}) {
        let state = this.progress.get(paramKey);
        
        if (!state) {
            state = {
                key: paramKey,
                status: ParameterStateManager.STATES.PENDING,
                instanceId: null,
                startedAt: null,
                completedAt: null,
                attempts: 0,
                lastError: null,
                result: null,
                metadata: {}
            };
            this.progress.set(paramKey, state);
        }

        // Update state
        state.status = status;
        
        if (metadata.instanceId !== undefined) {
            state.instanceId = metadata.instanceId;
        }
        
        if (metadata.startedAt) {
            state.startedAt = metadata.startedAt;
        }
        
        if (status === ParameterStateManager.STATES.COMPLETED) {
            state.completedAt = new Date();
            state.result = metadata.result || null;
        }
        
        if (status === ParameterStateManager.STATES.FAILED) {
            state.attempts = (state.attempts || 0) + 1;
            state.lastError = metadata.error || null;
            
            // Determine if should retry
            if (state.attempts < 3) {
                state.status = ParameterStateManager.STATES.RETRY;
            }
        }

        // Merge additional metadata
        if (metadata.metadata) {
            state.metadata = { ...state.metadata, ...metadata.metadata };
        }

        this.logger?.debug('📊 Parameter state updated', {
            paramKey,
            status,
            attempts: state.attempts
        });
    }

    /**
     * Mark parameter as completed
     */
    async markParameterCompleted(paramKey, result = null) {
        if (!await this.acquireLock()) {
            throw new Error('Failed to acquire lock for marking parameter completed');
        }

        try {
            this.setParameterState(paramKey, ParameterStateManager.STATES.COMPLETED, {
                result: result,
                instanceId: this.config.instanceId
            });

            // Remove from instance assigned params
            const instanceState = this.instanceStates.get(this.config.instanceId);
            if (instanceState) {
                instanceState.assignedParams = instanceState.assignedParams.filter(key => key !== paramKey);
                instanceState.lastHeartbeat = new Date();
            }

            await this.saveProgress();
            
            this.logger?.info('✅ Parameter completed', { paramKey, instanceId: this.config.instanceId });

        } finally {
            this.releaseLock();
        }
    }

    /**
     * Mark parameter as failed
     */
    async markParameterFailed(paramKey, error = null) {
        if (!await this.acquireLock()) {
            throw new Error('Failed to acquire lock for marking parameter failed');
        }

        try {
            this.setParameterState(paramKey, ParameterStateManager.STATES.FAILED, {
                error: error?.message || error?.toString() || 'Unknown error',
                instanceId: this.config.instanceId
            });

            // Remove from instance assigned params if max attempts reached
            const state = this.progress.get(paramKey);
            if (state && state.status === ParameterStateManager.STATES.FAILED) {
                const instanceState = this.instanceStates.get(this.config.instanceId);
                if (instanceState) {
                    instanceState.assignedParams = instanceState.assignedParams.filter(key => key !== paramKey);
                    instanceState.lastHeartbeat = new Date();
                }
            }

            await this.saveProgress();
            
            this.logger?.warn('❌ Parameter failed', { 
                paramKey, 
                error: error?.message || 'Unknown error',
                attempts: state?.attempts || 0
            });

        } finally {
            this.releaseLock();
        }
    }

    /**
     * Update instance heartbeat
     */
    async updateHeartbeat() {
        const instanceState = this.instanceStates.get(this.config.instanceId);
        if (instanceState) {
            instanceState.lastHeartbeat = new Date();
            instanceState.status = 'active';
            
            // Save periodically (not every heartbeat to avoid excessive I/O)
            if (Math.random() < 0.1) { // 10% chance
                await this.saveProgress();
            }
        }
    }

    /**
     * Cleanup stale processing states
     */
    async cleanupStaleProcessing() {
        const now = new Date();
        let cleanedCount = 0;
        let processedInstances = 0;

        // Check for stale instances
        for (const [instanceId, instanceState] of this.instanceStates) {
            if (instanceId === this.config.instanceId) continue; // Skip current instance

            // Skip instances that are already marked as stale to avoid repeated processing
            if (instanceState.status === 'stale') {
                continue;
            }

            const timeSinceHeartbeat = now.getTime() - (instanceState.lastHeartbeat?.getTime() || 0);

            if (timeSinceHeartbeat > this.config.instanceTimeout) {
                processedInstances++;

                // Count assigned parameters before clearing
                const assignedParamsCount = instanceState.assignedParams.length;

                // Mark instance as stale
                instanceState.status = 'stale';

                // Reset assigned parameters to pending
                for (const paramKey of instanceState.assignedParams) {
                    const state = this.progress.get(paramKey);
                    if (state && state.status === ParameterStateManager.STATES.PROCESSING) {
                        state.status = ParameterStateManager.STATES.PENDING;
                        state.instanceId = null;
                        cleanedCount++;
                    }
                }

                // Clear assigned parameters after processing
                instanceState.assignedParams = [];

                // Only log if there were actually parameters to clean up
                if (assignedParamsCount > 0) {
                    this.logger?.warn('🧹 Cleaned up stale instance', {
                        instanceId,
                        cleanedParams: assignedParamsCount
                    });
                }
            }
        }

        if (cleanedCount > 0) {
            this.logger?.info('🧹 Cleanup completed', {
                cleanedParams: cleanedCount,
                processedInstances
            });
        }
    }

    /**
     * Get count of active instances
     */
    getActiveInstanceCount() {
        let count = 0;
        const now = new Date();

        for (const instanceState of this.instanceStates.values()) {
            if (instanceState.status === 'active') {
                const timeSinceHeartbeat = now.getTime() - (instanceState.lastHeartbeat?.getTime() || 0);
                if (timeSinceHeartbeat < this.config.instanceTimeout) {
                    count++;
                } else {
                    // Instance is stale but not yet cleaned up, don't count it
                    this.logger?.debug('🔧 Found stale instance during count', {
                        instanceId: instanceState.instanceId,
                        timeSinceHeartbeat,
                        timeout: this.config.instanceTimeout
                    });
                }
            }
        }

        return count;
    }

    /**
     * Get count of pending parameters
     */
    getPendingCount() {
        let count = 0;
        
        for (const param of this.parameters) {
            const state = this.progress.get(param.key);
            if (!state || state.status === ParameterStateManager.STATES.PENDING || 
                state.status === ParameterStateManager.STATES.RETRY) {
                count++;
            }
        }
        
        return count;
    }

    /**
     * Get processing statistics
     */
    getStatistics() {
        const stats = {
            total: this.parameters.length,
            pending: 0,
            processing: 0,
            completed: 0,
            failed: 0,
            retry: 0,
            obsolete: 0,
            activeInstances: this.getActiveInstanceCount(),
            totalInstances: this.instanceStates.size
        };

        for (const param of this.parameters) {
            const state = this.progress.get(param.key);
            if (!state) {
                stats.pending++;
            } else {
                stats[state.status] = (stats[state.status] || 0) + 1;
            }
        }

        stats.completionRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;
        stats.errorRate = stats.total > 0 ? (stats.failed / stats.total) * 100 : 0;

        return stats;
    }

    /**
     * Start periodic tasks
     */
    startPeriodicTasks() {
        // Periodic state checks and cleanup
        this.stateCheckTimer = setInterval(async () => {
            try {
                await this.updateHeartbeat();
                await this.cleanupStaleProcessing();
            } catch (error) {
                this.logger?.error('❌ Periodic state check failed', { error: error.message });
            }
        }, this.config.stateCheckInterval);

        // Periodic cleanup of old instances
        this.cleanupTimer = setInterval(async () => {
            try {
                await this.cleanupOldInstances();
            } catch (error) {
                this.logger?.error('❌ Periodic cleanup failed', { error: error.message });
            }
        }, 300000); // Every 5 minutes
    }

    /**
     * Cleanup old instances
     */
    async cleanupOldInstances() {
        const now = new Date();
        const oneDayAgo = now.getTime() - 86400000; // 24 hours
        
        let removedCount = 0;
        
        for (const [instanceId, instanceState] of this.instanceStates) {
            if (instanceId === this.config.instanceId) continue; // Skip current instance
            
            const instanceAge = now.getTime() - (instanceState.startedAt?.getTime() || 0);
            const timeSinceHeartbeat = now.getTime() - (instanceState.lastHeartbeat?.getTime() || 0);
            
            if (instanceAge > oneDayAgo && timeSinceHeartbeat > this.config.instanceTimeout * 2) {
                this.instanceStates.delete(instanceId);
                removedCount++;
            }
        }

        if (removedCount > 0) {
            await this.saveProgress();
            this.logger?.info('🗑️ Removed old instances', { count: removedCount });
        }
    }

    /**
     * Save progress to file
     */
    async saveProgress() {
        if (!await this.acquireLock()) {
            throw new Error('Failed to acquire lock for saving progress');
        }

        try {
            const progressData = {
                lastUpdated: new Date().toISOString(),
                parameters: {},
                instances: {},
                statistics: this.getStatistics()
            };

            // Save parameter states
            for (const [key, state] of this.progress) {
                progressData.parameters[key] = {
                    status: state.status,
                    instanceId: state.instanceId,
                    startedAt: state.startedAt?.toISOString(),
                    completedAt: state.completedAt?.toISOString(),
                    attempts: state.attempts,
                    lastError: state.lastError,
                    result: state.result,
                    metadata: state.metadata
                };
            }

            // Save instance states
            for (const [instanceId, state] of this.instanceStates) {
                progressData.instances[instanceId] = {
                    status: state.status,
                    lastHeartbeat: state.lastHeartbeat?.toISOString(),
                    assignedParams: state.assignedParams,
                    startedAt: state.startedAt?.toISOString(),
                    metadata: state.metadata
                };
            }

            fs.writeFileSync(this.config.progressFile, JSON.stringify(progressData, null, 2));
            
        } finally {
            this.releaseLock();
        }
    }

    /**
     * Acquire file lock
     */
    async acquireLock() {
        // In development environment, disable file locking for testing
        if (this.config.environment === 'development') {
            this.logger?.debug('🔧 File locking disabled in development mode', {
                instanceId: this.config.instanceId,
                environment: this.config.environment
            });
            return true;
        }

        const startTime = Date.now();
        let waitTime = 50;
        const maxWaitTime = 500;

        while (Date.now() - startTime < this.config.lockTimeout) {
            try {
                // Check if lock file exists and is stale
                if (fs.existsSync(this.lockFile)) {
                    try {
                        const lockData = JSON.parse(fs.readFileSync(this.lockFile, 'utf8'));
                        const lockAge = Date.now() - lockData.timestamp;

                        // If lock is stale (older than timeout), remove it
                        if (lockAge > this.config.lockTimeout) {
                            this.logger?.debug('🔧 Removing stale lock file', {
                                lockAge,
                                timeout: this.config.lockTimeout,
                                staleLockInstanceId: lockData.instanceId,
                                currentInstanceId: this.config.instanceId
                            });
                            fs.unlinkSync(this.lockFile);
                        } else {
                            // Lock is still valid, wait and retry
                            await new Promise(resolve => setTimeout(resolve, waitTime));
                            waitTime = Math.min(waitTime * 2, maxWaitTime);
                            continue;
                        }
                    } catch (parseError) {
                        // If we can't parse the lock file, it's corrupted, remove it
                        this.logger?.debug('🔧 Removing corrupted lock file', { parseError: parseError.message });
                        fs.unlinkSync(this.lockFile);
                    }
                }

                // Try to create the lock file
                const lockData = { instanceId: this.config.instanceId, timestamp: Date.now() };
                fs.writeFileSync(this.lockFile, JSON.stringify(lockData), { flag: 'wx' });

                this.logger?.debug('🔒 Lock acquired successfully', { instanceId: this.config.instanceId });
                return true;

            } catch (error) {
                if (error.code === 'EEXIST') {
                    // Lock file was created by another process, wait and retry
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                    waitTime = Math.min(waitTime * 2, maxWaitTime);
                } else {
                    this.logger?.error('Lock acquisition error', { error: error.message, code: error.code });
                    return false;
                }
            }
        }

        this.logger?.warn('Failed to acquire lock within timeout', {
            timeout: this.config.lockTimeout,
            instanceId: this.config.instanceId
        });
        return false;
    }

    /**
     * Release file lock
     */
    releaseLock() {
        // In development environment, file locking is disabled
        if (this.config.environment === 'development') {
            this.logger?.debug('🔧 File locking disabled in development mode - no lock to release', {
                instanceId: this.config.instanceId,
                environment: this.config.environment
            });
            return;
        }

        try {
            if (fs.existsSync(this.lockFile)) {
                try {
                    const lockData = JSON.parse(fs.readFileSync(this.lockFile, 'utf8'));
                    if (lockData.instanceId === this.config.instanceId) {
                        fs.unlinkSync(this.lockFile);
                        this.logger?.debug('🔓 Lock released successfully', { instanceId: this.config.instanceId });
                    } else {
                        this.logger?.warn('🔓 Cannot release lock owned by another instance', {
                            lockOwner: lockData.instanceId,
                            currentInstance: this.config.instanceId
                        });
                    }
                } catch (parseError) {
                    // If we can't parse the lock file, remove it anyway
                    this.logger?.debug('🔓 Removing unparseable lock file', { parseError: parseError.message });
                    fs.unlinkSync(this.lockFile);
                }
            }
        } catch (error) {
            this.logger?.warn('Lock release error', { error: error.message, instanceId: this.config.instanceId });
        }
    }

    /**
     * Shutdown and cleanup
     */
    async shutdown() {
        // Clear timers
        if (this.stateCheckTimer) clearInterval(this.stateCheckTimer);
        if (this.cleanupTimer) clearInterval(this.cleanupTimer);

        // Mark instance as inactive
        const instanceState = this.instanceStates.get(this.config.instanceId);
        if (instanceState) {
            instanceState.status = 'inactive';
            instanceState.lastHeartbeat = new Date();
        }

        // Reset assigned parameters to pending
        if (instanceState && instanceState.assignedParams.length > 0) {
            for (const paramKey of instanceState.assignedParams) {
                const state = this.progress.get(paramKey);
                if (state && state.status === ParameterStateManager.STATES.PROCESSING) {
                    state.status = ParameterStateManager.STATES.PENDING;
                    state.instanceId = null;
                }
            }
            instanceState.assignedParams = [];
        }

        // Save final state
        try {
            await this.saveProgress();
        } catch (error) {
            this.logger?.error('❌ Failed to save progress during shutdown', { error: error.message });
        }

        this.logger?.info('🔄 Parameter State Manager shutdown completed');
    }
}

module.exports = ParameterStateManager;
