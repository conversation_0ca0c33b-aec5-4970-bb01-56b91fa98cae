const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * Comprehensive Health Monitoring System
 * Provides real-time monitoring of system health, performance metrics, and alerts
 */
class HealthMonitor {
    constructor(config = {}) {
        this.config = {
            healthCheckInterval: config.healthCheckInterval || 60000, // 1 minute
            metricsCollectionInterval: config.metricsCollectionInterval || 30000, // 30 seconds
            alertThresholds: {
                errorRate: 0.1, // 10%
                responseTime: 30000, // 30 seconds
                memoryUsage: 0.8, // 80%
                diskUsage: 0.9, // 90%
                cpuUsage: 0.8, // 80%
                ...config.alertThresholds
            },
            historyRetention: config.historyRetention || 86400000, // 24 hours
            instanceId: config.instanceId || `health_monitor_${process.pid}`,
            ...config
        };

        this.logger = config.logger;
        this.errorManager = config.errorManager;

        // Component references
        this.tokenManager = null;
        this.parameterManager = null;
        this.crawler = null;

        // Health data
        this.healthHistory = [];
        this.alerts = [];
        this.systemMetrics = {
            cpu: [],
            memory: [],
            disk: [],
            network: []
        };

        // Timers
        this.healthCheckTimer = null;
        this.metricsTimer = null;

        // Health status
        this.overallHealth = 'unknown';
        this.componentHealth = new Map();
        this.lastHealthCheck = null;

        // Alert cooldown tracking
        this.lastAlertTimes = new Map(); // 跟踪每种类型警告的最后触发时间
        this.consecutiveAlerts = new Map(); // 跟踪连续警告次数
    }

    /**
     * Initialize health monitor
     */
    initialize(components = {}) {
        this.tokenManager = components.tokenManager;
        this.parameterManager = components.parameterManager;
        this.crawler = components.crawler;

        this.startMonitoring();
        
        this.logger?.info('✅ Health Monitor initialized', {
            instanceId: this.config.instanceId,
            components: Object.keys(components)
        });
    }

    /**
     * Start monitoring processes
     */
    startMonitoring() {
        // Health checks
        this.healthCheckTimer = setInterval(() => {
            this.performHealthCheck().catch(error => {
                this.logger?.error('❌ Health check failed', { error: error.message });
            });
        }, this.config.healthCheckInterval);

        // Metrics collection
        this.metricsTimer = setInterval(() => {
            this.collectMetrics().catch(error => {
                this.logger?.error('❌ Metrics collection failed', { error: error.message });
            });
        }, this.config.metricsCollectionInterval);

        // Initial health check
        this.performHealthCheck().catch(error => {
            this.logger?.error('❌ Initial health check failed', { error: error.message });
        });
    }

    /**
     * Perform comprehensive health check
     */
    async performHealthCheck() {
        const healthCheck = {
            timestamp: new Date().toISOString(),
            instanceId: this.config.instanceId,
            overallHealth: 'healthy',
            components: {},
            systemMetrics: {},
            alerts: []
        };

        try {
            // Check system health
            healthCheck.systemMetrics = await this.checkSystemHealth();
            
            // Check component health
            if (this.tokenManager) {
                healthCheck.components.tokenManager = await this.checkTokenManagerHealth();
            }
            
            if (this.parameterManager) {
                healthCheck.components.parameterManager = await this.checkParameterManagerHealth();
            }
            
            if (this.crawler) {
                healthCheck.components.crawler = await this.checkCrawlerHealth();
            }

            if (this.errorManager) {
                healthCheck.components.errorManager = await this.checkErrorManagerHealth();
            }

            // Determine overall health
            healthCheck.overallHealth = this.calculateOverallHealth(healthCheck);
            
            // Check for alerts
            healthCheck.alerts = this.checkAlerts(healthCheck);

            // Store health data
            this.healthHistory.push(healthCheck);
            this.cleanupOldHistory();
            
            // Update component health tracking
            this.updateComponentHealth(healthCheck);
            
            this.overallHealth = healthCheck.overallHealth;
            this.lastHealthCheck = new Date();

            this.logger?.debug('🏥 Health check completed', {
                overallHealth: healthCheck.overallHealth,
                alerts: healthCheck.alerts.length
            });

            return healthCheck;

        } catch (error) {
            this.logger?.error('❌ Health check error', { error: error.message });
            healthCheck.overallHealth = 'critical';
            healthCheck.error = error.message;
            return healthCheck;
        }
    }

    /**
     * Check system health metrics
     */
    async checkSystemHealth() {
        const metrics = {
            memory: this.getMemoryUsage(),
            cpu: await this.getCPUUsage(),
            disk: this.getDiskUsage(),
            uptime: process.uptime(),
            nodeVersion: process.version,
            platform: process.platform,
            pid: process.pid
        };

        return metrics;
    }

    /**
     * Get memory usage information
     */
    getMemoryUsage() {
        const usage = process.memoryUsage();
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;

        // Calculate more accurate memory usage percentages
        const systemUsagePercent = Math.min(100, Math.max(0, (usedMemory / totalMemory) * 100));
        const processUsagePercent = usage.heapTotal > 0 ?
            Math.min(100, Math.max(0, (usage.heapUsed / usage.heapTotal) * 100)) : 0;

        return {
            processHeapUsed: usage.heapUsed,
            processHeapTotal: usage.heapTotal,
            processRSS: usage.rss,
            processExternal: usage.external,
            systemTotal: totalMemory,
            systemUsed: usedMemory,
            systemFree: freeMemory,
            systemUsagePercent: Math.round(systemUsagePercent * 100) / 100, // Round to 2 decimal places
            processUsagePercent: Math.round(processUsagePercent * 100) / 100
        };
    }

    /**
     * Get CPU usage information
     */
    async getCPUUsage() {
        return new Promise((resolve) => {
            const startUsage = process.cpuUsage();
            const startTime = Date.now();

            setTimeout(() => {
                const endUsage = process.cpuUsage(startUsage);
                const endTime = Date.now();
                const duration = endTime - startTime;

                const userPercent = (endUsage.user / 1000 / duration) * 100;
                const systemPercent = (endUsage.system / 1000 / duration) * 100;

                resolve({
                    user: userPercent,
                    system: systemPercent,
                    total: userPercent + systemPercent,
                    cores: os.cpus().length,
                    loadAverage: os.loadavg()
                });
            }, 100);
        });
    }

    /**
     * Get disk usage information
     */
    getDiskUsage() {
        try {
            const stats = fs.statSync(process.cwd());
            // This is a simplified version - in production you'd want to use a library
            // like 'diskusage' for accurate disk space information
            return {
                available: 'unknown',
                used: 'unknown',
                total: 'unknown',
                usagePercent: 0
            };
        } catch (error) {
            return {
                error: error.message,
                available: 0,
                used: 0,
                total: 0,
                usagePercent: 0
            };
        }
    }

    /**
     * Check token manager health
     */
    async checkTokenManagerHealth() {
        try {
            const stats = this.tokenManager.getStatistics();
            const health = {
                status: 'healthy',
                ...stats
            };

            // Determine health status
            if (stats.validTokens === 0) {
                health.status = 'critical';
                health.issue = 'No valid tokens available';
            } else if (stats.validTokens < 3) {
                health.status = 'warning';
                health.issue = 'Low number of valid tokens';
            } else if (stats.unhealthyTokens > stats.healthyTokens) {
                health.status = 'degraded';
                health.issue = 'More unhealthy tokens than healthy ones';
            }

            return health;
        } catch (error) {
            return {
                status: 'critical',
                error: error.message
            };
        }
    }

    /**
     * Check parameter manager health
     */
    async checkParameterManagerHealth() {
        try {
            const stats = this.parameterManager.getStatistics();
            const health = {
                status: 'healthy',
                ...stats
            };

            // Determine health status
            if (stats.activeInstances === 0 && stats.pending > 0) {
                health.status = 'warning';
                health.issue = 'No active instances processing pending parameters';
            } else if (stats.errorRate > 50) {
                health.status = 'critical';
                health.issue = 'High error rate in parameter processing';
            } else if (stats.errorRate > 20) {
                health.status = 'degraded';
                health.issue = 'Elevated error rate in parameter processing';
            }

            return health;
        } catch (error) {
            return {
                status: 'critical',
                error: error.message
            };
        }
    }

    /**
     * Check crawler health
     */
    async checkCrawlerHealth() {
        try {
            const stats = this.crawler.getStatistics();
            const health = {
                status: 'healthy',
                ...stats
            };

            // Determine health status
            if (!stats.isInitialized) {
                health.status = 'critical';
                health.issue = 'Crawler not initialized';
            } else if (stats.isShuttingDown) {
                health.status = 'warning';
                health.issue = 'Crawler is shutting down';
            } else if (stats.errorRate > 50) {
                health.status = 'critical';
                health.issue = 'High error rate in scraping';
            } else if (stats.errorRate > 20) {
                health.status = 'degraded';
                health.issue = 'Elevated error rate in scraping';
            } else if (stats.averageResponseTime > this.config.alertThresholds.responseTime) {
                health.status = 'degraded';
                health.issue = 'High average response time';
            }

            return health;
        } catch (error) {
            return {
                status: 'critical',
                error: error.message
            };
        }
    }

    /**
     * Check error manager health
     */
    async checkErrorManagerHealth() {
        try {
            const stats = this.errorManager.getErrorStatistics();
            const health = {
                status: 'healthy',
                ...stats
            };

            // Determine health status
            if (stats.errorRate > this.config.alertThresholds.errorRate) {
                health.status = 'critical';
                health.issue = 'High system error rate';
            } else if (Object.keys(stats.circuitBreakerStatus).length > 0) {
                health.status = 'warning';
                health.issue = 'Circuit breakers are active';
            }

            return health;
        } catch (error) {
            return {
                status: 'critical',
                error: error.message
            };
        }
    }

    /**
     * Calculate overall health from component health
     */
    calculateOverallHealth(healthCheck) {
        const statuses = [];

        // Add system health - 在仅警告模式下，资源使用率高不会导致critical状态
        if (this.config.resourceMonitoring?.warnOnlyMode) {
            // 仅警告模式：CPU和内存超标只标记为warning，不影响整体健康状态
            if (healthCheck.systemMetrics.memory?.systemUsagePercent > this.config.alertThresholds.memoryUsage * 100) {
                statuses.push('warning');
            }

            if (healthCheck.systemMetrics.cpu?.total > this.config.alertThresholds.cpuUsage * 100) {
                statuses.push('warning');
            }
        } else {
            // 标准模式：保持原有逻辑
            if (healthCheck.systemMetrics.memory?.systemUsagePercent > this.config.alertThresholds.memoryUsage * 100) {
                statuses.push('critical');
            }

            if (healthCheck.systemMetrics.cpu?.total > this.config.alertThresholds.cpuUsage * 100) {
                statuses.push('degraded');
            }
        }

        // Add component health
        for (const component of Object.values(healthCheck.components)) {
            if (component.status) {
                statuses.push(component.status);
            }
        }

        // Determine overall status
        if (statuses.includes('critical')) return 'critical';
        if (statuses.includes('degraded')) return 'degraded';
        if (statuses.includes('warning')) return 'warning';
        return 'healthy';
    }

    /**
     * Check for alerts based on thresholds
     */
    checkAlerts(healthCheck) {
        const alerts = [];
        const now = new Date();

        // Memory alerts - 仅警告，不退出
        if (healthCheck.systemMetrics.memory?.systemUsagePercent > this.config.alertThresholds.memoryUsage * 100) {
            const memoryAlert = {
                type: 'memory',
                severity: 'warning', // 改为warning，避免触发critical退出逻辑
                message: `内存使用率过高: ${healthCheck.systemMetrics.memory.systemUsagePercent.toFixed(1)}%`,
                messageEn: `High memory usage: ${healthCheck.systemMetrics.memory.systemUsagePercent.toFixed(1)}%`,
                timestamp: now.toISOString(),
                threshold: this.config.alertThresholds.memoryUsage * 100,
                actual: healthCheck.systemMetrics.memory.systemUsagePercent,
                action: 'monitor_only' // 标记为仅监控
            };

            // 检查是否在冷却期内
            if (this.shouldTriggerAlert('memory', memoryAlert)) {
                alerts.push(memoryAlert);
            }
        }

        // CPU alerts - 仅警告，不退出
        if (healthCheck.systemMetrics.cpu?.total > this.config.alertThresholds.cpuUsage * 100) {
            const cpuAlert = {
                type: 'cpu',
                severity: 'warning', // 保持warning级别
                message: `CPU使用率过高: ${healthCheck.systemMetrics.cpu.total.toFixed(1)}%`,
                messageEn: `High CPU usage: ${healthCheck.systemMetrics.cpu.total.toFixed(1)}%`,
                timestamp: now.toISOString(),
                threshold: this.config.alertThresholds.cpuUsage * 100,
                actual: healthCheck.systemMetrics.cpu.total,
                action: 'monitor_only' // 标记为仅监控
            };

            // 检查是否在冷却期内
            if (this.shouldTriggerAlert('cpu', cpuAlert)) {
                alerts.push(cpuAlert);
            }
        }

        // Component-specific alerts
        for (const [componentName, component] of Object.entries(healthCheck.components)) {
            if (component.status === 'critical') {
                alerts.push({
                    type: 'component',
                    component: componentName,
                    severity: 'critical',
                    message: `组件状态异常: ${componentName} - ${component.issue || '未知问题'}`,
                    messageEn: `${componentName} is in critical state: ${component.issue || 'Unknown issue'}`,
                    timestamp: now.toISOString()
                });
            }
        }

        // Store new alerts with enhanced logging
        for (const alert of alerts) {
            this.alerts.push(alert);

            // 使用中文日志格式
            if (alert.action === 'monitor_only') {
                this.logger?.warn(`🚨 资源监控警告: ${alert.message}`, {
                    ...alert,
                    note: '仅监控模式 - 不会退出实例'
                });

                // 额外的详细日志
                if (this.config.resourceMonitoring?.enableDetailedLogging) {
                    this.logDetailedResourceInfo(alert.type, healthCheck.systemMetrics);
                }
            } else {
                this.logger?.warn(`🚨 系统警告: ${alert.message}`, alert);
            }
        }

        // Cleanup old alerts
        this.cleanupOldAlerts();

        return alerts;
    }

    /**
     * Collect detailed metrics
     */
    async collectMetrics() {
        const metrics = {
            timestamp: new Date().toISOString(),
            memory: this.getMemoryUsage(),
            cpu: await this.getCPUUsage(),
            disk: this.getDiskUsage()
        };

        // Store metrics
        this.systemMetrics.memory.push(metrics.memory);
        this.systemMetrics.cpu.push(metrics.cpu);
        this.systemMetrics.disk.push(metrics.disk);

        // Keep only recent metrics
        const maxMetrics = 100;
        if (this.systemMetrics.memory.length > maxMetrics) {
            this.systemMetrics.memory = this.systemMetrics.memory.slice(-maxMetrics);
        }
        if (this.systemMetrics.cpu.length > maxMetrics) {
            this.systemMetrics.cpu = this.systemMetrics.cpu.slice(-maxMetrics);
        }
        if (this.systemMetrics.disk.length > maxMetrics) {
            this.systemMetrics.disk = this.systemMetrics.disk.slice(-maxMetrics);
        }
    }

    /**
     * Update component health tracking
     */
    updateComponentHealth(healthCheck) {
        for (const [componentName, health] of Object.entries(healthCheck.components)) {
            this.componentHealth.set(componentName, {
                status: health.status,
                lastCheck: new Date(),
                issue: health.issue || null
            });
        }
    }

    /**
     * Get current health status
     */
    getCurrentHealth() {
        return {
            overallHealth: this.overallHealth,
            lastHealthCheck: this.lastHealthCheck,
            componentHealth: Object.fromEntries(this.componentHealth),
            recentAlerts: this.alerts.slice(-10),
            systemMetrics: {
                memory: this.systemMetrics.memory.slice(-1)[0],
                cpu: this.systemMetrics.cpu.slice(-1)[0],
                disk: this.systemMetrics.disk.slice(-1)[0]
            }
        };
    }

    /**
     * Get health history
     */
    getHealthHistory(hours = 1) {
        const cutoff = new Date(Date.now() - hours * 3600000);
        return this.healthHistory.filter(h => new Date(h.timestamp) > cutoff);
    }

    /**
     * Get recent alerts
     */
    getRecentAlerts(hours = 1) {
        const cutoff = new Date(Date.now() - hours * 3600000);
        return this.alerts.filter(a => new Date(a.timestamp) > cutoff);
    }

    /**
     * Cleanup old history data
     */
    cleanupOldHistory() {
        const cutoff = new Date(Date.now() - this.config.historyRetention);
        this.healthHistory = this.healthHistory.filter(h => new Date(h.timestamp) > cutoff);
    }

    /**
     * Cleanup old alerts
     */
    cleanupOldAlerts() {
        const cutoff = new Date(Date.now() - this.config.historyRetention);
        this.alerts = this.alerts.filter(a => new Date(a.timestamp) > cutoff);
    }

    /**
     * Generate health report
     */
    generateHealthReport() {
        const currentHealth = this.getCurrentHealth();
        const recentHistory = this.getHealthHistory(24); // Last 24 hours
        const recentAlerts = this.getRecentAlerts(24);

        return {
            generatedAt: new Date().toISOString(),
            instanceId: this.config.instanceId,
            summary: {
                overallHealth: currentHealth.overallHealth,
                lastHealthCheck: currentHealth.lastHealthCheck,
                totalAlerts: recentAlerts.length,
                criticalAlerts: recentAlerts.filter(a => a.severity === 'critical').length
            },
            currentStatus: currentHealth,
            healthTrend: this.calculateHealthTrend(recentHistory),
            alerts: recentAlerts,
            recommendations: this.generateRecommendations(currentHealth, recentAlerts)
        };
    }

    /**
     * Calculate health trend
     */
    calculateHealthTrend(history) {
        if (history.length < 2) return 'insufficient_data';

        const recent = history.slice(-10);
        const healthScores = recent.map(h => {
            switch (h.overallHealth) {
                case 'healthy': return 4;
                case 'warning': return 3;
                case 'degraded': return 2;
                case 'critical': return 1;
                default: return 0;
            }
        });

        const average = healthScores.reduce((a, b) => a + b, 0) / healthScores.length;
        const firstHalf = healthScores.slice(0, Math.floor(healthScores.length / 2));
        const secondHalf = healthScores.slice(Math.floor(healthScores.length / 2));

        const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;

        if (secondAvg > firstAvg + 0.5) return 'improving';
        if (secondAvg < firstAvg - 0.5) return 'degrading';
        return 'stable';
    }

    /**
     * Generate recommendations based on health status
     */
    generateRecommendations(currentHealth, recentAlerts) {
        const recommendations = [];

        // Memory recommendations
        if (currentHealth.systemMetrics?.memory?.systemUsagePercent > 80) {
            recommendations.push({
                type: 'memory',
                priority: 'high',
                message: 'Consider reducing concurrent operations or increasing available memory'
            });
        }

        // Component recommendations
        for (const [component, health] of Object.entries(currentHealth.componentHealth)) {
            if (health.status === 'critical') {
                recommendations.push({
                    type: 'component',
                    component: component,
                    priority: 'critical',
                    message: `${component} requires immediate attention: ${health.issue}`
                });
            }
        }

        // Alert-based recommendations
        const criticalAlerts = recentAlerts.filter(a => a.severity === 'critical');
        if (criticalAlerts.length > 5) {
            recommendations.push({
                type: 'alerts',
                priority: 'high',
                message: 'High number of critical alerts - investigate system stability'
            });
        }

        return recommendations;
    }

    /**
     * 检查是否应该触发警告（考虑冷却时间）
     */
    shouldTriggerAlert(alertType, alert) {
        const now = Date.now();
        const cooldownPeriod = this.config.resourceMonitoring?.alertCooldown || 300000; // 5分钟默认冷却
        const lastAlertTime = this.lastAlertTimes.get(alertType) || 0;

        // 检查冷却时间
        if (now - lastAlertTime < cooldownPeriod) {
            return false;
        }

        // 更新最后警告时间
        this.lastAlertTimes.set(alertType, now);

        // 更新连续警告计数
        const currentCount = this.consecutiveAlerts.get(alertType) || 0;
        this.consecutiveAlerts.set(alertType, currentCount + 1);

        return true;
    }

    /**
     * 记录详细的资源信息
     */
    logDetailedResourceInfo(alertType, systemMetrics) {
        if (alertType === 'memory' && systemMetrics.memory) {
            const mem = systemMetrics.memory;
            this.logger?.info('📊 详细内存信息', {
                进程堆内存已用: `${(mem.processHeapUsed / 1024 / 1024).toFixed(2)} MB`,
                进程堆内存总计: `${(mem.processHeapTotal / 1024 / 1024).toFixed(2)} MB`,
                进程RSS: `${(mem.processRSS / 1024 / 1024).toFixed(2)} MB`,
                系统内存总计: `${(mem.systemTotal / 1024 / 1024 / 1024).toFixed(2)} GB`,
                系统内存已用: `${(mem.systemUsed / 1024 / 1024 / 1024).toFixed(2)} GB`,
                系统内存空闲: `${(mem.systemFree / 1024 / 1024 / 1024).toFixed(2)} GB`,
                系统使用率: `${mem.systemUsagePercent.toFixed(2)}%`,
                进程使用率: `${mem.processUsagePercent.toFixed(2)}%`
            });
        }

        if (alertType === 'cpu' && systemMetrics.cpu) {
            const cpu = systemMetrics.cpu;
            this.logger?.info('📊 详细CPU信息', {
                用户态CPU: `${cpu.user.toFixed(2)}%`,
                系统态CPU: `${cpu.system.toFixed(2)}%`,
                总CPU使用率: `${cpu.total.toFixed(2)}%`,
                CPU核心数: cpu.cores,
                系统负载: cpu.loadAverage.map(load => load.toFixed(2)).join(', ')
            });
        }
    }

    /**
     * 重置警告计数器（当资源使用率恢复正常时调用）
     */
    resetAlertCounters(alertType) {
        if (alertType) {
            this.consecutiveAlerts.set(alertType, 0);
        } else {
            this.consecutiveAlerts.clear();
        }
    }

    /**
     * 获取资源监控状态摘要
     */
    getResourceMonitoringSummary() {
        const summary = {
            监控状态: this.config.resourceMonitoring?.enabled ? '已启用' : '已禁用',
            警告模式: this.config.resourceMonitoring?.warnOnlyMode ? '仅警告' : '可能退出',
            警告冷却时间: `${(this.config.resourceMonitoring?.alertCooldown || 300000) / 1000}秒`,
            连续警告阈值: this.config.resourceMonitoring?.consecutiveAlertsBeforeAction || 5,
            当前连续警告: {}
        };

        for (const [type, count] of this.consecutiveAlerts.entries()) {
            summary.当前连续警告[type] = count;
        }

        return summary;
    }

    /**
     * Shutdown health monitor
     */
    shutdown() {
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
            this.healthCheckTimer = null;
        }

        if (this.metricsTimer) {
            clearInterval(this.metricsTimer);
            this.metricsTimer = null;
        }

        this.logger?.info('🔄 Health Monitor shutdown completed');
    }
}

module.exports = HealthMonitor;
