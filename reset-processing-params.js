const fs = require('fs');
const path = require('path');

/**
 * 重置所有处于processing状态的参数为pending状态
 * 清理过期的实例记录
 */

const PROGRESS_FILE = './progress.json';
const PROGRESS_LOCK_FILE = './progress.json.lock';

function acquireLock() {
    const maxWaitTime = 30000; // 30秒
    const startTime = Date.now();
    
    while (fs.existsSync(PROGRESS_LOCK_FILE)) {
        if (Date.now() - startTime > maxWaitTime) {
            console.log('⚠️ 锁文件等待超时，强制删除锁文件');
            fs.unlinkSync(PROGRESS_LOCK_FILE);
            break;
        }
        // 等待100ms
        require('child_process').execSync('sleep 0.1');
    }
    
    // 创建锁文件
    fs.writeFileSync(PROGRESS_LOCK_FILE, JSON.stringify({
        pid: process.pid,
        timestamp: new Date().toISOString(),
        operation: 'reset-processing-params'
    }));
}

function releaseLock() {
    if (fs.existsSync(PROGRESS_LOCK_FILE)) {
        fs.unlinkSync(PROGRESS_LOCK_FILE);
    }
}

function loadProgress() {
    if (!fs.existsSync(PROGRESS_FILE)) {
        console.log('❌ progress.json 文件不存在');
        return null;
    }
    
    try {
        const content = fs.readFileSync(PROGRESS_FILE, 'utf8');
        return JSON.parse(content);
    } catch (error) {
        console.error('❌ 读取 progress.json 失败:', error.message);
        return null;
    }
}

function saveProgress(progress) {
    try {
        fs.writeFileSync(PROGRESS_FILE, JSON.stringify(progress, null, 2));
        console.log('✅ progress.json 已更新');
        return true;
    } catch (error) {
        console.error('❌ 保存 progress.json 失败:', error.message);
        return false;
    }
}

function resetProcessingParams() {
    console.log('🔄 开始重置处于processing状态的参数...');
    
    try {
        // 获取锁
        acquireLock();
        
        // 加载进度文件
        const progress = loadProgress();
        if (!progress) {
            return false;
        }
        
        let resetCount = 0;
        let cleanedInstances = 0;
        
        // 统计当前状态
        const stats = {
            total: 0,
            pending: 0,
            processing: 0,
            completed: 0,
            failed: 0
        };
        
        // 重置processing状态的参数
        if (progress.parameters) {
            for (const [paramKey, state] of Object.entries(progress.parameters)) {
                stats.total++;

                if (state.status === 'processing') {
                    // 重置为pending状态
                    state.status = 'pending';
                    state.instanceId = null;
                    state.startedAt = null;
                    state.attempts = 0;
                    state.lastError = null;
                    resetCount++;
                    stats.pending++;
                } else {
                    stats[state.status] = (stats[state.status] || 0) + 1;
                }
            }
        }
        
        // 清理所有实例记录（因为没有活跃的实例）
        if (progress.instances) {
            cleanedInstances = Object.keys(progress.instances).length;
            progress.instances = {};
        }
        
        // 更新统计信息
        progress.stats = {
            ...progress.stats,
            lastUpdated: new Date().toISOString(),
            resetOperation: {
                timestamp: new Date().toISOString(),
                resetParams: resetCount,
                cleanedInstances: cleanedInstances
            }
        };
        
        // 保存更新后的进度
        const saved = saveProgress(progress);
        
        if (saved) {
            console.log('\n📊 重置操作完成:');
            console.log(`   总参数数量: ${stats.total}`);
            console.log(`   重置的参数: ${resetCount}`);
            console.log(`   清理的实例: ${cleanedInstances}`);
            console.log(`   当前状态分布:`);
            console.log(`     - pending: ${stats.pending}`);
            console.log(`     - completed: ${stats.completed}`);
            console.log(`     - failed: ${stats.failed}`);
            
            return true;
        }
        
        return false;
        
    } catch (error) {
        console.error('❌ 重置操作失败:', error.message);
        return false;
    } finally {
        // 释放锁
        releaseLock();
    }
}

// 主函数
function main() {
    console.log('🚀 开始重置处于processing状态的参数...\n');
    
    const success = resetProcessingParams();
    
    if (success) {
        console.log('\n✅ 重置操作成功完成！');
        console.log('💡 现在可以重新启动爬虫进程了');
    } else {
        console.log('\n❌ 重置操作失败');
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { resetProcessingParams };
