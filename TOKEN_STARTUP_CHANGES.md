# Token 启动逻辑修改说明

## 修改概述

根据用户需求，修改了 tokens 的逻辑，使得每次启动时默认 tokensFile 中的所有 token 都是有效的。这样做的目的是允许用户通过其他脚本更新 token，而不需要在启动时重新验证。

## 修改的文件

### 1. `src/managers/TokenLifecycleManager.js`

#### 主要修改：

1. **修改 `initialize()` 方法**：
   - 移除了启动时的 `validateAllTokens()` 调用
   - 添加了新的 `markAllTokensValidAtStartup()` 调用
   - 更新了日志信息，说明所有 token 在启动时被标记为有效

2. **添加 `markAllTokensValidAtStartup()` 方法**：
   - 将所有具有有效 JWT 结构的 token 标记为有效
   - 重置 `invalidAttempts` 为 0
   - 清除 `invalidReason` 和 `bannedUntil`
   - 设置 `lastValidated` 为当前时间
   - 更新 token 健康状态

3. **修改 `loadTokens()` 方法**：
   - 默认所有 token 的 `isValid` 为 `true`
   - 重置 `invalidAttempts` 为 0
   - 清除 `bannedUntil` 和 `invalidReason`
   - 即使 JWT 结构无效，也在启动时保持为有效状态（但会记录警告）

4. **添加 `stopPeriodicTasks()` 方法**：
   - 清理所有定时器（validation、health check、rotation）
   - 用于测试和优雅关闭

### 2. `TokenManager.js`

#### 主要修改：

1. **修改 `_loadTokensInternal()` 方法**：
   - 将默认的 `isValid` 从 `null` 改为 `true`
   - 确保除非明确设置为 `false`，否则所有 token 都被标记为有效

## 修改前后对比

### 修改前：
- 启动时会调用 `validateAllTokens()` 验证所有 token
- 可能会将一些 token 标记为无效
- 需要等待验证完成才能使用 token

### 修改后：
- 启动时默认所有 token 都是有效的
- 通过 `markAllTokensValidAtStartup()` 重置 token 状态
- 立即可用，无需等待验证
- 支持外部脚本更新 token 的场景

## 兼容性

- 保持了原有的定期验证机制（通过 `startPeriodicTasks()`）
- 保持了运行时的 token 验证逻辑
- 只修改了启动时的行为
- 向后兼容现有的配置和使用方式

## 测试验证

通过测试脚本验证了修改的正确性：
- TokenLifecycleManager: 17 个 token 全部标记为有效
- TokenManager: 17 个 token 全部可用
- 所有测试通过

## 使用场景

这个修改特别适用于以下场景：
1. 使用外部脚本定期更新 token
2. 希望快速启动，不等待 token 验证
3. 在开发环境中频繁重启应用
4. 需要确保启动时所有 token 都可用

## 注意事项

- 定期验证仍然会在后台运行，确保 token 的长期有效性
- 如果 token 确实无效，会在后续的使用或定期验证中被发现
- 建议配合外部 token 更新脚本使用，确保 token 的实际有效性
