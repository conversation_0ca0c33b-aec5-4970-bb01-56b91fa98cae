const fs = require('fs');
const path = require('path');

/**
 * 重置所有参数状态为pending
 * 因为progress.json中的completed状态与实际文件不符
 */

const PROGRESS_FILE = './progress.json';
const PROGRESS_LOCK_FILE = './progress.json.lock';

function acquireLock() {
    const maxWaitTime = 30000; // 30秒
    const startTime = Date.now();
    
    while (fs.existsSync(PROGRESS_LOCK_FILE)) {
        if (Date.now() - startTime > maxWaitTime) {
            console.log('⚠️ 锁文件等待超时，强制删除锁文件');
            fs.unlinkSync(PROGRESS_LOCK_FILE);
            break;
        }
        // 等待100ms
        require('child_process').execSync('sleep 0.1');
    }
    
    // 创建锁文件
    fs.writeFileSync(PROGRESS_LOCK_FILE, JSON.stringify({
        pid: process.pid,
        timestamp: new Date().toISOString(),
        operation: 'reset-all-params'
    }));
}

function releaseLock() {
    if (fs.existsSync(PROGRESS_LOCK_FILE)) {
        fs.unlinkSync(PROGRESS_LOCK_FILE);
    }
}

function loadProgress() {
    if (!fs.existsSync(PROGRESS_FILE)) {
        console.log('❌ progress.json 文件不存在');
        return null;
    }
    
    try {
        const content = fs.readFileSync(PROGRESS_FILE, 'utf8');
        return JSON.parse(content);
    } catch (error) {
        console.error('❌ 读取 progress.json 失败:', error.message);
        return null;
    }
}

function saveProgress(progress) {
    try {
        fs.writeFileSync(PROGRESS_FILE, JSON.stringify(progress, null, 2));
        console.log('✅ progress.json 已更新');
        return true;
    } catch (error) {
        console.error('❌ 保存 progress.json 失败:', error.message);
        return false;
    }
}

function resetAllParams() {
    console.log('🔄 开始重置所有参数状态...');
    
    try {
        // 获取锁
        acquireLock();
        
        // 加载进度文件
        const progress = loadProgress();
        if (!progress) {
            return false;
        }
        
        let resetCount = 0;
        let cleanedInstances = 0;
        
        // 统计当前状态
        const beforeStats = {
            total: 0,
            pending: 0,
            processing: 0,
            completed: 0,
            failed: 0
        };
        
        // 统计重置前的状态
        if (progress.parameters) {
            for (const [paramKey, state] of Object.entries(progress.parameters)) {
                beforeStats.total++;
                beforeStats[state.status] = (beforeStats[state.status] || 0) + 1;
            }
        }
        
        console.log('\n📊 重置前状态分布:');
        console.log(`   总参数数量: ${beforeStats.total}`);
        console.log(`   pending: ${beforeStats.pending}`);
        console.log(`   processing: ${beforeStats.processing}`);
        console.log(`   completed: ${beforeStats.completed}`);
        console.log(`   failed: ${beforeStats.failed}`);
        
        // 重置所有参数状态
        if (progress.parameters) {
            for (const [paramKey, state] of Object.entries(progress.parameters)) {
                if (state.status !== 'pending') {
                    // 重置为pending状态
                    state.status = 'pending';
                    state.instanceId = null;
                    state.startedAt = null;
                    state.attempts = 0;
                    state.lastError = null;
                    state.result = null;
                    resetCount++;
                }
            }
        }
        
        // 清理所有实例记录
        if (progress.instances) {
            cleanedInstances = Object.keys(progress.instances).length;
            progress.instances = {};
        }
        
        // 更新统计信息
        progress.stats = {
            ...progress.stats,
            lastUpdated: new Date().toISOString(),
            fullResetOperation: {
                timestamp: new Date().toISOString(),
                resetParams: resetCount,
                cleanedInstances: cleanedInstances,
                beforeStats: beforeStats
            }
        };
        
        // 保存更新后的进度
        const saved = saveProgress(progress);
        
        if (saved) {
            console.log('\n📊 重置操作完成:');
            console.log(`   总参数数量: ${beforeStats.total}`);
            console.log(`   重置的参数: ${resetCount}`);
            console.log(`   清理的实例: ${cleanedInstances}`);
            console.log(`   重置后状态: 所有参数都是 pending 状态`);
            
            return true;
        }
        
        return false;
        
    } catch (error) {
        console.error('❌ 重置操作失败:', error.message);
        return false;
    } finally {
        // 释放锁
        releaseLock();
    }
}

// 主函数
function main() {
    console.log('🚀 开始重置所有参数状态...\n');
    
    console.log('⚠️ 警告: 这将重置所有参数状态为pending，包括标记为completed的参数');
    console.log('   原因: progress.json中的completed状态与实际文件不符\n');
    
    const success = resetAllParams();
    
    if (success) {
        console.log('\n✅ 重置操作成功完成！');
        console.log('💡 现在可以重新启动爬虫进程了');
        console.log('🔄 所有参数都将重新开始爬取');
    } else {
        console.log('\n❌ 重置操作失败');
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = { resetAllParams };
