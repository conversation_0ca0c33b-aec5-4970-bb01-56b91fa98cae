const EnhancedPlaywrightCrawler = require('./src/scrapers/EnhancedPlaywrightCrawler');

// 测试URL构建逻辑
function testURLBuilding() {
    console.log('🧪 测试URL构建逻辑...');
    
    // 创建一个临时的爬虫实例来测试URL构建
    const crawler = new EnhancedPlaywrightCrawler({
        baseURL: 'https://zj.stzy.com/create-paper/chapter'
    });
    
    // 测试参数
    const testParameters = {
        studyPhaseCode: '1',
        subjectCode: '2',
        textbookVersionCode: '3',
        catalogCode: '4',
        chapterCode: '5',
        sectionCode: '6'
    };
    
    // 构建URL
    const baseURL = 'https://zj.stzy.com/create-paper/chapter';
    const targetURL = crawler.buildTargetURL(baseURL, testParameters);
    
    console.log('📍 基础URL:', baseURL);
    console.log('📍 构建的目标URL:', targetURL);
    
    // 验证URL是否包含所有参数
    const url = new URL(targetURL);
    const params = url.searchParams;
    
    console.log('🔍 URL参数检查:');
    console.log('  - studyPhaseCode:', params.get('studyPhaseCode'));
    console.log('  - subjectCode:', params.get('subjectCode'));
    console.log('  - textbookVersionCode:', params.get('textbookVersionCode'));
    console.log('  - catalogCode:', params.get('catalogCode'));
    console.log('  - chapterCode:', params.get('chapterCode'));
    console.log('  - sectionCode:', params.get('sectionCode'));
    
    // 检查是否所有参数都正确设置
    const expectedParams = ['studyPhaseCode', 'subjectCode', 'textbookVersionCode', 'catalogCode', 'chapterCode', 'sectionCode'];
    const allParamsPresent = expectedParams.every(param => params.has(param));
    
    if (allParamsPresent) {
        console.log('✅ URL构建测试成功！所有参数都正确包含在URL中');
    } else {
        console.log('❌ URL构建测试失败！某些参数缺失');
    }
    
    return targetURL;
}

// 运行测试
try {
    const url = testURLBuilding();
    console.log('\n🎉 测试完成！生成的URL:', url);
} catch (error) {
    console.error('❌ 测试失败:', error.message);
}
