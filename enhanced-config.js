/**
 * Enhanced Web Scraping System Configuration
 * Comprehensive configuration for all system components
 */

const environment = process.env.NODE_ENV || 'development';

module.exports = {
    // Application settings
    app: {
        name: 'Enhanced Web Scraping System',
        version: '2.0.0',
        environment: process.env.NODE_ENV || 'development',
        instanceId: `instance_${process.pid}_${Date.now()}`
    },

    // File paths
    files: {
        tokensFile: './tokens.json',
        paramsFile: './params.json',
        progressFile: './progress.json',
        outputDir: './output',
        logsDir: './logs',
        tempDir: './temp'
    },

    // Browser settings
    browser: {
        headless: process.env.SCRAPER_HEADLESS || false,
        timeout: parseInt(process.env.SCRAPER_TIMEOUT) || 100000,
        pageTimeout: parseInt(process.env.SCRAPER_PAGE_TIMEOUT) || 100000,
        maxInstances: parseInt(process.env.SCRAPER_MAX_BROWSER_INSTANCES) || 1,
        launchOptions: {
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        }
    },

    // Scraping settings
    scraping: {
        baseURL: 'https://zj.stzy.com/create-paper/chapter',
        apiURL: 'https://qms.stzy.com/matrix/zw-search/api/v1/homeEs/question/textbookQuery',
        minDelay: parseInt(process.env.SCRAPER_MIN_DELAY) || 1000,
        maxDelay: parseInt(process.env.SCRAPER_MAX_DELAY) || 3000,
        maxRetries: parseInt(process.env.SCRAPER_MAX_RETRIES) || 3,
        retryDelay: parseInt(process.env.SCRAPER_RETRY_DELAY) || 5000,
        requestTimeout: parseInt(process.env.SCRAPER_REQUEST_TIMEOUT) || 30000,
        maxConcurrentRequests: parseInt(process.env.SCRAPER_MAX_CONCURRENT) || 3,
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    },

    // Token management
    tokens: {
        validationInterval: parseInt(process.env.TOKEN_VALIDATION_INTERVAL) || 300000, // 5 minutes
        maxInvalidAttempts: parseInt(process.env.TOKEN_MAX_INVALID_ATTEMPTS) || 3,
        rotationInterval: parseInt(process.env.TOKEN_ROTATION_INTERVAL) || 3600000, // 1 hour
        healthCheckInterval: parseInt(process.env.TOKEN_HEALTH_CHECK_INTERVAL) || 60000, // 1 minute
        lockTimeout: parseInt(process.env.TOKEN_LOCK_TIMEOUT) || 30000
    },

    // Development settings
    ignoreTokenExpiration: environment === 'development', // Skip token expiration check in development

    // Parameter management
    parameters: {
        maxConcurrentInstances: parseInt(process.env.PARAM_MAX_CONCURRENT_INSTANCES) || 10,
        instanceTimeout: parseInt(process.env.PARAM_INSTANCE_TIMEOUT) || 1800000, // 30 minutes (increased from 2 minutes)
        stateCheckInterval: parseInt(process.env.PARAM_STATE_CHECK_INTERVAL) || 120000, // 2 minutes (increased from 30 seconds)
        lockTimeout: parseInt(process.env.PARAM_LOCK_TIMEOUT) || 30000,
        batchSize: parseInt(process.env.PARAM_BATCH_SIZE) || 10
    },

    // Error handling
    errors: {
        maxRetryAttempts: parseInt(process.env.ERROR_MAX_RETRY_ATTEMPTS) || 3,
        retryBackoffMultiplier: parseFloat(process.env.ERROR_RETRY_BACKOFF_MULTIPLIER) || 2,
        circuitBreakerThreshold: parseInt(process.env.ERROR_CIRCUIT_BREAKER_THRESHOLD) || 5,
        circuitBreakerTimeout: parseInt(process.env.ERROR_CIRCUIT_BREAKER_TIMEOUT) || 300000, // 5 minutes
        errorReportingEnabled: process.env.ERROR_REPORTING_ENABLED !== 'false'
    },

    // Logging
    logging: {
        level: process.env.LOG_LEVEL || 'DEBUG',
        format: process.env.LOG_FORMAT || 'json',
        maxFileSize: process.env.LOG_MAX_FILE_SIZE || '10MB',
        maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5,
        enableConsole: process.env.LOG_ENABLE_CONSOLE !== 'false',
        enableFile: process.env.LOG_ENABLE_FILE !== 'false',
        enableStructured: process.env.LOG_ENABLE_STRUCTURED !== 'false'
    },

    // Monitoring
    monitoring: {
        healthCheckInterval: parseInt(process.env.MONITOR_HEALTH_CHECK_INTERVAL) || 60000, // 1 minute
        metricsCollectionInterval: parseInt(process.env.MONITOR_METRICS_INTERVAL) || 30000, // 30 seconds
        alertThresholds: {
            errorRate: parseFloat(process.env.MONITOR_ERROR_RATE_THRESHOLD) || 0.1, // 10%
            responseTime: parseInt(process.env.MONITOR_RESPONSE_TIME_THRESHOLD) || 30000, // 30 seconds
            memoryUsage: parseFloat(process.env.MONITOR_MEMORY_USAGE_THRESHOLD) || 0.8, // 80%
            diskUsage: parseFloat(process.env.MONITOR_DISK_USAGE_THRESHOLD) || 0.9, // 90%
            cpuUsage: parseFloat(process.env.MONITOR_CPU_USAGE_THRESHOLD) || 0.8 // 80%
        },
        historyRetention: parseInt(process.env.MONITOR_HISTORY_RETENTION) || 86400000, // 24 hours
        // 资源监控配置 - 仅警告，不退出实例
        resourceMonitoring: {
            enabled: true,
            warnOnlyMode: true, // 仅警告模式，不会导致程序退出
            alertCooldown: parseInt(process.env.MONITOR_ALERT_COOLDOWN) || 300000, // 5分钟警告冷却时间
            consecutiveAlertsBeforeAction: parseInt(process.env.MONITOR_CONSECUTIVE_ALERTS) || 5, // 连续5次警告才考虑采取行动
            enableDetailedLogging: true // 启用详细的资源使用日志
        }
    },

    // Proxy configuration (direct configuration, referencing config.js pattern)
    proxy: {
        enabled: true, // Set to true to enable proxy
        host: '127.0.0.1',
        port: 1080,
        protocol: 'http',
        // auth: {
        //     username: 'your_username',
        //     password: 'your_password'
        // },
        rotationInterval: 600000, // 10 minutes
        healthCheckInterval: 120000, // 2 minutes
        maxFailures: 3,
        list: [] // Add proxy list if using multiple proxies
    }
};
